# Maven Enforcer Plugin 安全配置

## 概述

本项目已配置 Maven Enforcer Plugin 来自动检测和阻止有安全漏洞的依赖，特别是老版本的 log4j。

## 配置的安全规则

### 1. 禁止老版本 log4j
- `log4j:log4j` - 完全禁止老版本 log4j（存在 CVE-2021-44228, CVE-2021-45046 等严重漏洞）
- `log4j:log4j:*:*:compile` - 禁止编译时依赖
- `log4j:log4j:*:*:runtime` - 禁止运行时依赖

### 2. 禁止有安全问题的 Log4j 2.x 版本
- `org.apache.logging.log4j:log4j-core:[2.0,2.17.0)` - 禁止 2.17.0 以下版本
- `org.apache.logging.log4j:log4j-api:[2.0,2.17.0)` - 禁止 2.17.0 以下版本

### 3. Java 版本要求
- 要求 Java 21 或更高版本

## 当前项目状态

✅ **安全状态：良好**
- 使用 Log4j 2.23.1（安全版本）
- 没有检测到老版本 log4j 依赖
- 所有模块都通过了安全检查

## 如何使用

### 自动检查
Enforcer 在以下 Maven 生命周期阶段自动运行：
- `mvn validate` - 验证阶段
- `mvn compile` - 编译阶段
- `mvn package` - 打包阶段
- `mvn install` - 安装阶段

### 手动检查
```bash
# 检查所有模块的依赖安全性
mvn validate

# 检查特定模块
mvn validate -pl mospital-common

# 显示详细的依赖树
mvn dependency:tree
```

### 检查结果示例

**成功情况：**
```
[INFO] Rule 0: org.apache.maven.enforcer.rules.dependency.BannedDependencies passed
[INFO] Rule 1: org.apache.maven.enforcer.rules.version.RequireJavaVersion passed
```

**检测到安全问题：**
```
[ERROR] Rule 0: org.apache.maven.enforcer.rules.dependency.BannedDependencies failed with message:
[ERROR] 检测到禁用的依赖！
[ERROR] - 老版本的 log4j (groupId: log4j) 存在严重安全漏洞 (CVE-2021-44228, CVE-2021-45046 等)
[ERROR] - Log4j 2.x 版本低于 2.17.0 也存在安全问题
[ERROR] 请使用 Log4j 2.17.0 或更高版本 (当前项目使用 2.23.1)
[ERROR] org.mospital:module-name:jar:1.0.0
[ERROR]    log4j:log4j:jar:1.2.17 <--- banned via the exclude/include list
```

## 配置位置

安全规则配置在根目录的 `pom.xml` 文件中：

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-enforcer-plugin</artifactId>
    <version>3.4.1</version>
    <executions>
        <execution>
            <id>enforce-banned-dependencies</id>
            <goals>
                <goal>enforce</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
                <rules>
                    <bannedDependencies>
                        <excludes>
                            <!-- 禁止老版本的 log4j -->
                            <exclude>log4j:log4j</exclude>
                            <!-- 禁止有安全问题的 Log4j 2.x 版本 -->
                            <exclude>org.apache.logging.log4j:log4j-core:[2.0,2.17.0)</exclude>
                            <exclude>org.apache.logging.log4j:log4j-api:[2.0,2.17.0)</exclude>
                        </excludes>
                    </bannedDependencies>
                    <requireJavaVersion>
                        <version>[21,)</version>
                    </requireJavaVersion>
                </rules>
                <fail>true</fail>
            </configuration>
        </execution>
    </executions>
</plugin>
```

## 如果检测到安全问题

1. **立即停止构建** - Enforcer 会阻止构建继续进行
2. **查看错误信息** - 确定哪个依赖有问题
3. **更新依赖版本** - 升级到安全版本
4. **检查传递性依赖** - 使用 `mvn dependency:tree` 查看依赖来源
5. **排除有问题的依赖** - 如果必要，在 pom.xml 中排除有问题的传递性依赖

## 维护建议

1. **定期更新** - 定期检查并更新 Log4j 版本
2. **监控安全公告** - 关注 Apache Log4j 安全公告
3. **扩展规则** - 根据需要添加其他有安全问题的依赖到禁止列表
4. **CI/CD 集成** - 确保 CI/CD 流水线包含 `mvn validate` 步骤

## 依赖管理优化

### Log4j BOM 使用

项目已优化为使用 Log4j BOM (Bill of Materials) 进行统一版本管理：

```xml
<!-- 在 dependencyManagement 中导入 BOM -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-bom</artifactId>
    <version>${log4j.version}</version>
    <type>pom</type>
    <scope>import</scope>
</dependency>
```

**优势：**
- ✅ 统一版本管理，避免版本冲突
- ✅ 子模块无需指定版本号
- ✅ 减少配置冗余
- ✅ 确保所有 Log4j 组件版本一致

**子模块使用方式：**
```xml
<!-- 子模块中只需声明 groupId 和 artifactId -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-api</artifactId>
    <!-- 版本从 BOM 自动继承 -->
</dependency>
```

## 相关链接

- [Apache Log4j 安全公告](https://logging.apache.org/log4j/2.x/security.html)
- [Maven Enforcer Plugin 文档](https://maven.apache.org/enforcer/maven-enforcer-plugin/)
- [CVE-2021-44228 详情](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44228)
- [Maven BOM 最佳实践](https://maven.apache.org/guides/introduction/introduction-to-dependency-mechanism.html#bill-of-materials-bom-poms)
