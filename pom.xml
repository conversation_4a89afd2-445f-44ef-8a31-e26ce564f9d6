<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.mospital</groupId>
    <artifactId>mospital-parent</artifactId>
    <version>1.0.0</version>
    <modules>
        <module>mospital-erhc-guanxin</module>
        <module>mospital-jackson</module>
        <module>mospital-common</module>
        <module>mospital-bsoft</module>
        <module>mospital-zktec</module>
        <module>mospital-wecity-mip</module>
        <module>mospital-bsoft-mip</module>
        <module>mospital-alipay</module>
        <module>mospital-dongruan</module>
        <module>mospital-dongruan-yibao</module>
        <module>mospital-zlsoft</module>
        <module>mospital-donggang</module>
        <module>mospital-tencent-health-card</module>
        <module>mospital-erhc-yilianzhong</module>
        <module>mospital-dongruan-lis-print</module>
        <module>mospital-shine</module>
        <module>mospital-yonyou-invoice</module>
        <module>mospital-allinpay</module>
        <module>mospital-dongruan-lis-pdf</module>
        <module>mospital-guoguang-ccb</module>
        <module>mospital-bsoft-hai</module>
        <module>mospital-witontek</module>
        <module>mospital-dongruan-weining</module>
        <module>mospital-dongruan-pay</module>
        <module>mospital-bosi-ebill</module>
        <module>mospital-weining-pay</module>
        <module>mospital-mip</module>
        <module>mospital-ftimage</module>
        <module>mospital-shine-http</module>
        <module>mospital-neusoft</module>
        <module>mospital-shine-checkin-ws</module>
        <module>mospital-dongruan-qingtui</module>
        <module>mospital-yahua</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <kotlin.code.style>official</kotlin.code.style>
        <kotlin.compiler.languageVersion>2.0</kotlin.compiler.languageVersion>
        <kotlin.compiler.apiVersion>2.0</kotlin.compiler.apiVersion>
        <kotlin.compiler.jvmTarget>21</kotlin.compiler.jvmTarget>
        <kotlin.compiler.javaParameters>true</kotlin.compiler.javaParameters>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>21</java.version>

        <mospital.version>1.0.0</mospital.version>
        <kotlin.version>2.0.21</kotlin.version>
        <kotlin-coroutines.version>1.9.0</kotlin-coroutines.version>
        <junit.version>5.12.1</junit.version>
        <hutool.version>6.0.0-M17</hutool.version>
        <jackson.version>2.17.2</jackson.version>
        <retrofit.version>2.11.0</retrofit.version>
        <okhttp.version>4.12.0</okhttp.version>
        <bc.version>1.78.1</bc.version>
        <weixin.version>4.7.3.B</weixin.version>
        <alipay.version>4.39.234.ALL</alipay.version>

        <cxf.version>4.0.5</cxf.version>

        <jaxb.version>4.0.2</jaxb.version>
        <jaxws.version>4.0.2</jaxws.version>

        <fastjson.version>2.0.53</fastjson.version>
        <slf4j.version>2.0.16</slf4j.version>
        <log4j.version>2.24.3</log4j.version>
        <joox.version>2.0.1</joox.version>
        <guava.version>33.3.0-jre</guava.version>
        <dom4j.version>2.1.3</dom4j.version>
        <commons-codec.version>1.18.0</commons-codec.version>
        <commons-io.version>2.19.0</commons-io.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <version>${kotlin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-bom</artifactId>
                <version>${kotlin-coroutines.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
            <dependency>
                <groupId>org.dromara.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jaxb3</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcpkix-jdk18on -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bc.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-common</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-jackson</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-wecity-mip</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-dongruan</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-dongruan-yibao</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-erhc-yilianzhong</artifactId>
                <version>${mospital.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-dongruan-lis-print</artifactId>
                <version>${mospital.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>dom4j</groupId>
                        <artifactId>dom4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-reload4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-slf4j2-impl -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j2-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.jooq/joox -->
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>joox</artifactId>
                <version>${joox.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/jakarta.xml.bind/jakarta.xml.bind-api -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.glassfish.jaxb/jaxb-runtime -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>4.0.5</version>
                <scope>runtime</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/jakarta.xml.ws/jakarta.xml.ws-api -->
            <dependency>
                <groupId>jakarta.xml.ws</groupId>
                <artifactId>jakarta.xml.ws-api</artifactId>
                <version>${jaxws.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.sun.xml.ws/jaxws-rt -->
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-rt</artifactId>
                <version>4.0.3</version>
                <scope>runtime</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-maven-plugin</artifactId>
                    <version>${kotlin.version}</version>
                    <executions>
                        <execution>
                            <id>compile</id>
                            <goals>
                                <goal>compile</goal> <!-- You can skip the <goals> element
                        if you enable extensions for the plugin -->
                            </goals>
                            <configuration>
                                <sourceDirs>
                                    <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                </sourceDirs>
                            </configuration>
                        </execution>
                        <execution>
                            <id>test-compile</id>
                            <goals>
                                <goal>test-compile</goal> <!-- You can skip the <goals> element
                    if you enable extensions for the plugin -->
                            </goals>
                            <configuration>
                                <sourceDirs>
                                    <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                </sourceDirs>
                            </configuration>
                        </execution>
                    </executions>
                    <configuration>
                        <jvmTarget>${kotlin.compiler.jvmTarget}</jvmTarget>
                        <languageVersion>${kotlin.compiler.languageVersion}</languageVersion>
                        <apiVersion>${kotlin.compiler.apiVersion}</apiVersion>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <release>${java.version}</release>
                    </configuration>
                    <executions>
                        <!-- Replacing default-compile as it is treated specially by Maven -->
                        <execution>
                            <id>default-compile</id>
                            <phase>none</phase>
                        </execution>
                        <!-- Replacing default-testCompile as it is treated specially by Maven -->
                        <execution>
                            <id>default-testCompile</id>
                            <phase>none</phase>
                        </execution>
                        <execution>
                            <id>java-compile</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>compile</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>java-test-compile</id>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.0</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.4.1</version>
                    <executions>
                        <execution>
                            <id>enforce-banned-dependencies</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <phase>validate</phase>
                            <configuration>
                                <rules>
                                    <bannedDependencies>
                                        <excludes>
                                            <!-- 禁止老版本的 log4j (存在安全漏洞) -->
                                            <exclude>log4j:log4j</exclude>
                                            <!-- 禁止有安全问题的 Log4j 2.x 版本 -->
                                            <exclude>org.apache.logging.log4j:log4j-core:[2.0,2.17.0)</exclude>
                                            <exclude>org.apache.logging.log4j:log4j-api:[2.0,2.17.0)</exclude>
                                        </excludes>
                                        <message>
                                            检测到禁用的依赖！
                                            - 老版本的 log4j (groupId: log4j) 存在严重安全漏洞 (CVE-2021-44228, CVE-2021-45046 等)
                                            - Log4j 2.x 版本低于 2.17.0 也存在安全问题
                                            请使用 Log4j 2.17.0 或更高版本 (当前项目使用 ${log4j.version})
                                        </message>
                                    </bannedDependencies>
                                    <requireJavaVersion>
                                        <version>[21,)</version>
                                        <message>项目要求 Java 21 或更高版本</message>
                                    </requireJavaVersion>
                                </rules>
                                <fail>true</fail>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 启用 Maven Enforcer Plugin 进行依赖安全检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>