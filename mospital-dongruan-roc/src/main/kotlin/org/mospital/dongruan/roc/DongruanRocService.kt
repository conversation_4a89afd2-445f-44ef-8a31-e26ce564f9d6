package org.mospital.dongruan.roc

import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * 东软ROC系统集成平台服务接口
 */
interface DongruanRocService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(DongruanRocService::class.java)
        private val serviceCache = mutableMapOf<DongruanRocConfig, DongruanRocService>()

        /**
         * 统一 Header 拦截器
         * 自动为所有请求添加 domain 和 key header
         */
        private class HeaderInterceptor(
            private val domain: String,
            private val key: String
        ) : Interceptor {
            override fun intercept(chain: Interceptor.Chain): Response {
                val originalRequest = chain.request()
                val requestWithHeaders = originalRequest.newBuilder()
                    .header("domain", domain)
                    .header("key", key)
                    .build()
                return chain.proceed(requestWithHeaders)
            }
        }

        private fun createService(config: DongruanRocConfig): DongruanRocService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = config.connectTimeout,
                readTimeout = config.readTimeout,
                useUuidRequestId = true
            ).newBuilder()
                .addInterceptor(HeaderInterceptor(config.domain, config.key))
                .build()
                
            val retrofit = Retrofit.Builder()
                .baseUrl(config.url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .addCallAdapterFactory(ResultCallAdapterFactory())
                .build()
            return retrofit.create(DongruanRocService::class.java)
        }

        @Synchronized
        fun getService(config: DongruanRocConfig): DongruanRocService {
            return serviceCache.getOrPut(config) { createService(config) }
        }

        val me: DongruanRocService by lazy {
            getService(DongruanRocConfig.me)
        }
    }

    /**
     * 【RC1.1.12S012】查询药品物价项目列表
     * @param vagueName 项目名称
     * @param simpleSpell 拼音码
     * @param identifyKey 项目编码
     * @param sysClassList 系统类别
     * @return 药品物价列表
     */
    @GET("/roc/curr-web/api/v1/curr/pharmaceutical/drug/query")
    fun queryDrugPriceList(
        @Query("vagueName") vagueName: String? = null,
        @Query("simpleSpell") simpleSpell: String? = null,
        @Query("identifyKey") identifyKey: String? = null,
        @Query("sysClassList") sysClassList: String? = null
    ): Result<List<DrugPriceInfo>>

} 