package org.mospital.dongruan.roc

import org.mospital.jackson.ConfigLoader

/**
 * 东软ROC系统配置
 */
data class DongruanRocConfig(
    val url: String,
    val domain: String,
    val key: String,
    val connectTimeout: Long = 2_000L,
    val readTimeout: Long = 10_000L
) {
    companion object {
        val me: DongruanRocConfig by lazy {
            ConfigLoader.loadConfig("dongruan-roc.yaml", DongruanRocConfig::class.java, DongruanRocConfig::class.java)
        }
    }
}
