package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * 药品物价信息
 * 对应接口：【RC1.1.12S012】查询药品物价项目列表
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class DrugPriceInfo(
    /**
     * 药品名称
     */
    val name: String?,
    
    /**
     * 单价
     */
    val price: String?,
    
    /**
     * 规格
     */
    val specs: String?,
    
    /**
     * 单位
     */
    val unit: String?,
    
    /**
     * 生产厂家编码
     */
    val producerCode: String?,
    
    /**
     * 生产厂家名称
     */
    val producerName: String?,
    
    /**
     * 执行科室编码
     */
    val execDeptCode: String?,
    
    /**
     * 执行科室名称
     */
    val execDeptName: String?,
    
    /**
     * 标识主码（项目编码）
     */
    val identifyKey: String?,
    
    /**
     * 商品码拼音码
     */
    val spellCode: String?,
    
    /**
     * 有效性标识（1 在用 0停用 2 废弃）
     */
    val validState: String?,
    
    /**
     * 药品性质（M麻醉 J1精神一 J2精神二 D毒 F放射 YZD易制毒 N非特殊管理）
     */
    val drugQuality: String?,
    
    /**
     * 医保类别：0 未知，1甲类 2乙类 3丙类
     */
    val specialFlag9: String?,
    
    /**
     * 剂型编码
     */
    val doseModelCode: String?,
    
    /**
     * 包装数
     */
    val packQty: String?,
    
    /**
     * 剂型名称
     */
    val doseModelName: String?,
    
    /**
     * 通用名
     */
    val regularName: String?,
    
    /**
     * 药品类别
     */
    val drugType: String?,
    
    /**
     * 药品类型名称
     */
    val drugTypeName: String?
) {
    /**
     * 判断药品是否有效
     */
    val isValid: Boolean
        get() = validState == "1"
    
    /**
     * 获取单价（数值类型）
     */
    val priceValue: Double?
        get() = try {
            price?.toDouble()
        } catch (e: NumberFormatException) {
            null
        }
    
    /**
     * 获取包装数（数值类型）
     */
    val packQtyValue: Int?
        get() = try {
            packQty?.toInt()
        } catch (e: NumberFormatException) {
            null
        }
} 