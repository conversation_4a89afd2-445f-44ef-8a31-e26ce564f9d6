package org.mospital.dongruan.roc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * ROC系统统一响应格式
 * @param T 响应数据类型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class DongruanRocResponse<T>(
    /**
     * 响应码
     */
    val code: Int,
    
    /**
     * 响应消息
     */
    val msg: String,
    
    /**
     * 响应数据
     */
    val data: T?
) {
    /**
     * 判断是否请求成功
     */
    val isSuccess: Boolean
        get() = code == 200
    
    /**
     * 转换为Result
     */
    fun toResult(): kotlin.Result<T> {
        return if (isSuccess && data != null) {
            kotlin.Result.success(data)
        } else {
            kotlin.Result.failure(RuntimeException("请求失败: $msg (code: $code)"))
        }
    }
} 