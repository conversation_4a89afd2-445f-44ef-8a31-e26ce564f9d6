package org.mospital.dongruan.roc

import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory

/**
 * ROC服务测试类
 */
class RocServiceTest {

    private val logger = LoggerFactory.getLogger(RocServiceTest::class.java)

    @Test
    fun testQueryDrugPriceList() {
        // 测试查询药品物价项目列表
        val result = DongruanRocService.me.queryDrugPriceList(
            vagueName = "阿莫西林"
        )
        
        result.fold(
            onSuccess = { drugList ->
                logger.info("查询成功，共获取到 ${drugList.size} 条药品信息")
                drugList.forEach { drug ->
                    logger.info("药品：${drug.name}，价格：${drug.price}，规格：${drug.specs}")
                }
            },
            onFailure = { exception ->
                logger.error("查询失败：${exception.message}", exception)
            }
        )
    }

    @Test
    fun testQueryDrugPriceListBySpellCode() {
        // 测试通过拼音码查询
        val result = DongruanRocService.me.queryDrugPriceList(
            simpleSpell = "AMXL"
        )
        
        result.fold(
            onSuccess = { drugList ->
                logger.info("通过拼音码查询成功，共获取到 ${drugList.size} 条药品信息")
                drugList.forEach { drug ->
                    logger.info("药品：${drug.name}，拼音码：${drug.spellCode}，价格：${drug.price}")
                }
            },
            onFailure = { exception ->
                logger.error("查询失败：${exception.message}", exception)
            }
        )
    }

    @Test
    fun testQueryDrugPriceListByIdentifyKey() {
        // 测试通过项目编码查询
        val result = DongruanRocService.me.queryDrugPriceList(
            identifyKey = "Y00000006619"
        )
        
        result.fold(
            onSuccess = { drugList ->
                logger.info("通过项目编码查询成功，共获取到 ${drugList.size} 条药品信息")
                drugList.forEach { drug ->
                    logger.info("药品：${drug.name}，编码：${drug.identifyKey}，价格：${drug.price}")
                }
            },
            onFailure = { exception ->
                logger.error("查询失败：${exception.message}", exception)
            }
        )
    }

    @Test
    fun testQueryAllDrugPriceList() {
        // 测试查询所有药品物价（无过滤条件）
        val result = DongruanRocService.me.queryDrugPriceList()
        
        result.fold(
            onSuccess = { drugList ->
                logger.info("查询所有药品成功，共获取到 ${drugList.size} 条药品信息")
                // 只显示前5条
                drugList.take(5).forEach { drug ->
                    logger.info("药品：${drug.name}，价格：${drug.price}，状态：${if(drug.isValid) "有效" else "无效"}")
                }
            },
            onFailure = { exception ->
                logger.error("查询失败：${exception.message}", exception)
            }
        )
    }
} 