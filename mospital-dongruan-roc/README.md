# 东软ROC系统集成模块

本模块提供与东软ROC（远程对象调用）系统集成平台的接口实现，支持查询药品物价、非药品物价等基础数据功能。

## 功能特性

- ✅ 查询药品物价项目列表 (RC1.1.12S012)
- 🚧 查询非药品物价项目列表 (RC1.1.13S013) - 待实现
- 🚧 其他ROC接口 - 待实现

## 配置说明

在 `src/main/resources/dongruan-roc.yaml` 文件中配置连接参数：

```yaml
# 服务基础URL
url: "http://127.0.0.1:10011/roc/curr-web"

# 接口域名
domain: "NEU_HZFW"

# 接口密钥
key: "your-api-key-here"

# 超时配置
connectTimeout: 2000
readTimeout: 10000
```

## 使用示例

### 查询药品物价信息

```kotlin
import org.mospital.dongruan.roc.RocService

// 1. 根据药品名称模糊查询
val result1 = RocService.queryDrugPriceList(vagueName = "阿莫西林")

// 2. 根据拼音码查询
val result2 = RocService.queryDrugPriceList(simpleSpell = "AMXL")

// 3. 根据项目编码精确查询
val result3 = RocService.queryDrugPriceList(identifyKey = "Y00000006619")

// 4. 组合查询条件
val result4 = RocService.queryDrugPriceList(
    vagueName = "阿莫西林",
    drugType = "P"  // P=西药
)

// 处理查询结果
result1.fold(
    onSuccess = { drugList ->
        drugList.forEach { drug ->
            println("药品：${drug.name}")
            println("价格：${drug.price} 元/${drug.unit}")
            println("规格：${drug.specs}")
            println("生产厂家：${drug.producerName}")
            println("有效状态：${if(drug.isValid) "有效" else "无效"}")
            println("---")
        }
    },
    onFailure = { exception ->
        println("查询失败：${exception.message}")
    }
)
```

### 药品信息字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| name | String | 药品名称 |
| price | String | 单价 |
| specs | String | 规格 |
| unit | String | 单位 |
| producerName | String | 生产厂家名称 |
| identifyKey | String | 标识主码（项目编码） |
| spellCode | String | 商品码拼音码 |
| validState | String | 有效性标识（1:在用 0:停用 2:废弃） |
| drugQuality | String | 药品性质（M:麻醉 J1:精神一 J2:精神二 D:毒 F:放射 YZD:易制毒 N:非特殊管理） |
| specialFlag9 | String | 医保类别（0:未知 1:甲类 2:乙类 3:丙类） |
| drugType | String | 药品类别 |
| drugTypeName | String | 药品类型名称 |

### 便捷方法

```kotlin
// 检查药品是否有效
val isValid = drug.isValid

// 获取数值类型的价格
val priceValue = drug.priceValue  // Double?

// 获取数值类型的包装数
val packQtyValue = drug.packQtyValue  // Int?
```

## API接口对照

基于curl请求示例：

```bash
curl -s "http://127.0.0.1:10011/roc/curr-web/api/v1/curr/pharmaceutical/drug/query?vagueName=%E9%98%BF%E8%8E%AB%E8%A5%BF%E6%9E%97" \
     -H 'domain: NEU_HZFW' \
     -H 'key: f69897d6-d8a7-4c84-ac86-cca3b463b249'
```

对应的Kotlin调用：

```kotlin
val result = RocService.queryDrugPriceList(
    vagueName = "阿莫西林",
    domain = "NEU_HZFW",
    key = "f69897d6-d8a7-4c84-ac86-cca3b463b249"
)
```

## 错误处理

接口返回的是 `kotlin.Result<List<DrugPriceInfo>>` 类型，包含成功和失败两种状态：

- **成功 (Success)**：返回药品列表数据
- **失败 (Failure)**：返回异常信息，可能的原因：
  - 网络连接超时
  - 服务器返回错误状态码
  - 接口密钥验证失败
  - JSON解析异常

## 依赖说明

本模块依赖以下组件：

- `mospital-common`：公共HTTP客户端和配置加载器
- `mospital-jackson`：JSON序列化支持
- `retrofit2`：HTTP客户端框架
- `kotlinx-coroutines`：协程支持

## 测试

运行测试用例：

```bash
./gradlew :mospital-dongruan-roc:test
```

测试用例包含：
- 按药品名称查询
- 按拼音码查询  
- 按项目编码查询
- 查询所有药品信息

## 注意事项

1. 确保ROC系统服务可访问
2. 检查接口域名和密钥配置正确
3. 网络超时时间根据实际情况调整
4. 大批量查询时注意接口调用频率限制 