<!DOCTYPE html>
      <html>
      <head>
      <title>cdr-service</title>
      <meta charset="utf-8" />
      <style>@charset "UTF-8";
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.2);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  font-size: 13px;
  line-height: 25px;
  color: #393838;
  position: relative;
}

table {
  margin: 10px 0 15px 0;
  border-collapse: collapse;
}

td,
th {
  border: 1px solid #ddd;
  padding: 3px 10px;
}

th {
  padding: 5px 10px;
}

a, a:link, a:visited {
  color: #34495e;
  text-decoration: none;
}

a:hover, a:focus {
  color: #59d69d;
  text-decoration: none;
}

a img {
  border: none;
}

p {
  padding-left: 10px;
  margin-bottom: 9px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #404040;
  line-height: 36px;
}

h1 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
  line-height: 50px;
}

h2 {
  font-size: 28px;
  padding-top: 10px;
  padding-bottom: 10px;
}

h3 {
  clear: both;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  border-left: 3px solid #59d69d;
  padding-left: 8px;
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 13px;
}

hr {
  margin: 0 0 19px;
  border: 0;
  border-bottom: 1px solid #ccc;
}

blockquote {
  padding: 13px 13px 21px 15px;
  margin-bottom: 18px;
  font-family: georgia, serif;
  font-style: italic;
}

blockquote:before {
  font-size: 40px;
  margin-left: -10px;
  font-family: georgia, serif;
  color: #eee;
}

blockquote p {
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  margin-bottom: 0;
  font-style: italic;
}

code,
pre {
  font-family: Monaco, Andale Mono, Courier New, monospace;
}

code {
  background-color: #fee9cc;
  color: rgba(0, 0, 0, 0.75);
  padding: 1px 3px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

pre {
  display: block;
  padding: 14px;
  margin: 0 0 18px;
  line-height: 16px;
  font-size: 11px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f6f6f6;
}

pre code {
  background-color: #f6f6f6;
  color: #737373;
  font-size: 11px;
  padding: 0;
}

sup {
  font-size: 0.83em;
  vertical-align: super;
  line-height: 0;
}

* {
  -webkit-print-color-adjust: exact;
}

@media print {
  body,
  code,
  pre code,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black;
  }

  table,
  pre {
    page-break-inside: avoid;
  }
}
html,
body {
  height: 100%;
}

.table-of-contents {
  position: fixed;
  top: 61px;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  width: 260px;
}

.table-of-contents > ul > li > a {
  font-size: 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.table-of-contents ul {
  overflow: auto;
  margin: 0px;
  height: 100%;
  padding: 0px 0px;
  box-sizing: border-box;
  list-style-type: none;
}

.table-of-contents ul li {
  padding-left: 20px;
}

.table-of-contents a {
  padding: 2px 0px;
  display: block;
  text-decoration: none;
}

.content-right {
  max-width: 700px;
  margin-left: 290px;
  padding-left: 70px;
  flex-grow: 1;
}
.content-right h2:target {
  padding-top: 80px;
}

body > p {
  margin-left: 30px;
}

body > table {
  margin-left: 30px;
}

body > pre {
  margin-left: 30px;
}

.curProject {
  position: fixed;
  top: 20px;
  font-size: 25px;
  color: black;
  margin-left: -240px;
  width: 240px;
  padding: 5px;
  line-height: 25px;
  box-sizing: border-box;
}

.g-doc {
  margin-top: 56px;
  padding-top: 24px;
  display: flex;
}

.curproject-name {
  font-size: 42px;
}

.m-header {
  background: #32363a;
  height: 56px;
  line-height: 56px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
}
.m-header .title {
  font-size: 22px;
  color: #fff;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  margin-left: 16px;
  padding: 0;
  line-height: 56px;
  border: none;
}
.m-header .nav {
  color: #fff;
  font-size: 16px;
  position: absolute;
  right: 32px;
  top: 0;
}
.m-header .nav a {
  color: #fff;
  margin-left: 16px;
  padding: 8px;
  transition: color .2s;
}
.m-header .nav a:hover {
  color: #59d69d;
}

.m-footer {
  border-top: 1px solid #ddd;
  padding-top: 16px;
  padding-bottom: 16px;
}

/*# sourceMappingURL=defaultTheme.css.map */
</style>
      </head>
      <body>
        <div class="m-header">
          <a href="#" style="display: inherit;"><svg class="svg" width="32px" height="32px" viewBox="0 0 64 64" version="1.1"><title>Icon</title><desc>Created with Sketch.</desc><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1"><stop stop-color="#FFFFFF" offset="0%"></stop><stop stop-color="#F2F2F2" offset="100%"></stop></linearGradient><circle id="path-2" cx="31.9988602" cy="31.9988602" r="2.92886048"></circle><filter x="-85.4%" y="-68.3%" width="270.7%" height="270.7%" filterUnits="objectBoundingBox" id="filter-3"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.159703351 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter></defs><g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="大屏幕"><g id="Icon"><circle id="Oval-1" fill="url(#linearGradient-1)" cx="32" cy="32" r="32"></circle><path d="M36.7078009,31.8054514 L36.7078009,51.7110548 C36.7078009,54.2844537 34.6258634,56.3695395 32.0579205,56.3695395 C29.4899777,56.3695395 27.4099998,54.0704461 27.4099998,51.7941246 L27.4099998,31.8061972 C27.4099998,29.528395 29.4909575,27.218453 32.0589004,27.230043 C34.6268432,27.241633 36.7078009,29.528395 36.7078009,31.8054514 Z" id="blue" fill="#2359F1" fill-rule="nonzero"></path><path d="M45.2586091,17.1026914 C45.2586091,17.1026914 45.5657231,34.0524383 45.2345291,37.01141 C44.9033351,39.9703817 43.1767091,41.6667796 40.6088126,41.6667796 C38.040916,41.6667796 35.9609757,39.3676862 35.9609757,37.0913646 L35.9609757,17.1034372 C35.9609757,14.825635 38.0418959,12.515693 40.6097924,12.527283 C43.177689,12.538873 45.2586091,14.825635 45.2586091,17.1026914 Z" id="green" fill="#57CF27" fill-rule="nonzero" transform="translate(40.674608, 27.097010) rotate(60.000000) translate(-40.674608, -27.097010) "></path><path d="M28.0410158,17.0465598 L28.0410158,36.9521632 C28.0410158,39.525562 25.9591158,41.6106479 23.3912193,41.6106479 C20.8233227,41.6106479 18.7433824,39.3115545 18.7433824,37.035233 L18.7433824,17.0473055 C18.7433824,14.7695034 20.8243026,12.4595614 23.3921991,12.4711513 C25.9600956,12.4827413 28.0410158,14.7695034 28.0410158,17.0465598 Z" id="red" fill="#FF561B" fill-rule="nonzero" transform="translate(23.392199, 27.040878) rotate(-60.000000) translate(-23.392199, -27.040878) "></path><g id="inner-round"><use fill="black" fill-opacity="1" filter="url(#filter-3)" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use><use fill="#F7F7F7" fill-rule="evenodd" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use></g></g></g></g></svg></a>
          <a href="#"><h1 class="title">YAPI 接口文档</h1></a>
          <div class="nav">
            <a href="https://hellosean1025.github.io/yapi/">YApi</a>
          </div>
        </div>
        <div class="g-doc">
          <div class="table-of-contents"><ul><li><a href="#1120lis">1.1 Lis</a><ul><li><a href="#20111u3010rd111s236u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544a28cdru5e93290a3ca20id3d20111u3010rd111s236u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544a28cdru5e93293e203ca3e"> 1.1.1【RD1.1.1S236】查询患者Lis常规检验报告(Cdr库)
<a id= 1.1.1【RD1.1.1S236】查询患者Lis常规检验报告(Cdr库)> </a></a></li><li><a href="#112u3010rd112s237u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544au660eu7ec628cdru5e93290a3ca20id3d112u3010rd112s237u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544au660eu7ec628cdru5e93293e203ca3e">1.1.2【RD1.1.2S237】查询患者Lis常规检验报告明细(Cdr库)
<a id=1.1.2【RD1.1.2S237】查询患者Lis常规检验报告明细(Cdr库)> </a></a></li><li><a href="#113u3010rd113s238u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544a0a3ca20id3d113u3010rd113s238u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544a3e203ca3e">1.1.3【RD1.1.3S238】查询患者Lis微生物报告
<a id=1.1.3【RD1.1.3S238】查询患者Lis微生物报告> </a></a></li><li><a href="#114u3010rd114s239u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544au660eu7ec60a3ca20id3d114u3010rd114s239u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544au660eu7ec63e203ca3e">1.1.4【RD1.1.4S239】查询患者Lis微生物报告明细
<a id=1.1.4【RD1.1.4S239】查询患者Lis微生物报告明细> </a></a></li></ul></li><li><a href="#1220pacs">1.2 pacs</a><ul><li><a href="#121u3010rd121s240u3011u67e5u8be2pacsu68c0u67e5u62a5u544a28cdru5e93290a3ca20id3d121u3010rd121s240u3011u67e5u8be2pacsu68c0u67e5u62a5u544a28cdru5e93293e203ca3e">1.2.1【RD1.2.1S240】查询Pacs检查报告(Cdr库)
<a id=1.2.1【RD1.2.1S240】查询Pacs检查报告(Cdr库)> </a></a></li><li><a href="#122u3010rd122s241u3011u67e5u8be2u60a3u8005pacsu75c5u7406u62a5u544a0a3ca20id3d122u3010rd122s241u3011u67e5u8be2u60a3u8005pacsu75c5u7406u62a5u544a3e203ca3e">1.2.2【RD1.2.2S241】查询患者Pacs病理报告
<a id=1.2.2【RD1.2.2S241】查询患者Pacs病理报告> </a></a></li></ul></li></ul></div>
          <div id="right" class="content-right">
           <h1 class="curproject-name"> cdr-service </h1> 
<h1 id="1120lis">1.1 Lis</h1>
<p></p>
<h2 id="20111u3010rd111s236u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544a28cdru5e93290a3ca20id3d20111u3010rd111s236u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544a28cdru5e93293e203ca3e"> 1.1.1【RD1.1.1S236】查询患者Lis常规检验报告(Cdr库)
<a id= 1.1.1【RD1.1.1S236】查询患者Lis常规检验报告(Cdr库)> </a></h2>
<p></p>
<h3 id="">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/lis/detail</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-2">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>branchCode</td>
<td>否</td>
<td></td>
<td>院区编码</td>
</tr>
<tr>
<td>patientId</td>
<td>是</td>
<td></td>
<td>患者ID</td>
</tr>
<tr>
<td>visitedId</td>
<td>否</td>
<td></td>
<td>就诊ID 门诊患者传门诊流水号,住院患者传住院院流水号</td>
</tr>
<tr>
<td>startTime</td>
<td>否</td>
<td></td>
<td>开始时间 yyyy/MM/dd HH24:mi:ss</td>
</tr>
<tr>
<td>endTime</td>
<td>否</td>
<td></td>
<td>结束时间 yyyy/MM/dd HH24:mi:ss</td>
</tr>
</tbody>
</table>
<h3 id="-3">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> approveTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本核准时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> approverId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本核准者编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> approverName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本核准者姓名</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> barCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">条形码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execSqn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行单号</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hisItemIdList</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费项目编码(^分隔)</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hisItemNameList</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费项目名称列表(^分隔)</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">id</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inceptTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验科接收时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告名</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResult</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验结果</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验报告流水号</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构英文名称</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本级机构</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构名称</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isGcp</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否药物临床试验</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验项目编码</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验项目名称</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> lisMemo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">LIS备注信息</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> machineCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机器码</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者ID</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientSex</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者性别</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resourceFile</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告资源文件URL、PDF</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结果时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">样本号</span></td><td key=5></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本状态</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">采样时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">样本类型</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specimenName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本名称</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主键testCode</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitTypeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">就诊类型</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">就诊ID</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"approveTime":</span><span style="color: green;">""</span>,<span style="color: green;">"approverId":</span><span style="color: green;">""</span>,<span style="color: green;">"approverName":</span><span style="color: green;">""</span>,<span style="color: green;">"barCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"execSqn":</span><span style="color: green;">""</span>,<span style="color: green;">"hisItemIdList":</span><span style="color: green;">""</span>,<span style="color: green;">"hisItemNameList":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"inceptTime":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionName":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResult":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionSn":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionEname":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionId":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionName":</span><span style="color: green;">""</span>,<span style="color: green;">"isGcp":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"lisMemo":</span><span style="color: green;">""</span>,<span style="color: green;">"machineCode":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"orderTime":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientSex":</span><span style="color: green;">""</span>,<span style="color: green;">"resourceFile":</span><span style="color: green;">""</span>,<span style="color: green;">"resultTime":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleId":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleState":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleTime":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleType":</span><span style="color: green;">""</span>,<span style="color: green;">"specimenName":</span><span style="color: green;">""</span>,<span style="color: green;">"testCode":</span><span style="color: green;">""</span>,<span style="color: green;">"visitTypeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="112u3010rd112s237u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544au660eu7ec628cdru5e93290a3ca20id3d112u3010rd112s237u3011u67e5u8be2u60a3u8005lisu5e38u89c4u68c0u9a8cu62a5u544au660eu7ec628cdru5e93293e203ca3e">1.1.2【RD1.1.2S237】查询患者Lis常规检验报告明细(Cdr库)
<a id=1.1.2【RD1.1.2S237】查询患者Lis常规检验报告明细(Cdr库)> </a></h2>
<p></p>
<h3 id="-4">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/lis/result</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-5">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者id</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> endTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结束时间 yyyy/MM/dd HH24:mi:ss</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> startTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开始时间 yyyy/MM/dd HH24:mi:ss</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> testCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验结果主表</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> visitNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者流水号</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> visitTypeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">1：门诊 2：住院</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"cardNo":"","endTime":"","startTime":"","testCode":"","visitNo":"","visitTypeCode":""}</pre>
<h3 id="-6">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> alertFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">alertFlag 危急值标识 VARCHAR2(50) 可为空</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> approverId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本核准者编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> approverName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本核准者姓名</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> barcode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">barcode 条码号 VARCHAR2(50) th_test_record_detail.BAR_CODE</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> comItemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">大项目编码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> comItemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">大项目名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> encounterClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">encounterClass 业务来源 CHAR(2) 可为空</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hisItemId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">hisItemId 收费项目编码 VARCHAR2(50) 可为空</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">id 主键id VARCHAR2(100) th_test_record_detail. ID</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">inspectionDate 检验时间 DATE th_test_record_detail. TEST_DATE</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResult</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">inspectionResult 检验结果 VARCHAR2(100) th_test_record_detail.VALUE</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResultRange</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">inspectionResultRange 参考范围 VARCHAR2(800) th_test_record_detail. VALUE_RANG</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResultUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">inspectionResultUnit 检验结果单位名称 VARCHAR2(100) th_test_record_detail. ITEM__UNIT</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">inspectionSn 检验报告流水号 VARCHAR2(50) th_test_record_detail. ID</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionename</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionename 机构英文名称 VARCHAR2(200) 可为空</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionid</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionid 本级机构 VARCHAR2(50) 集团内统一时，为空</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionname 机构名称 VARCHAR2(200) 可为空</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemCode 检验项目编码 VARCHAR2(50) th_test_record_detail.ITEM_CODE</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemEname 项目名称英文 VARCHAR2(100) 可为空</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemMethod</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemMethod 检验方法 VARCHAR2(100) 可为空</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemName 检验项目名称 VARCHAR2(300) th_test_record_detail.ITEM_NAME</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> machineId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">machineId 仪器ID VARCHAR2(50) th_test_record_detail. MACHINE_CODE</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">ordSn 医嘱流水号 VARCHAR2(50) th_test_record_detail. ORDER_ID</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">patientId 患者ID VARCHAR2(50) 5.0为CARD_NO th_test_record_detail. PATIENT_ID</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientSex</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者性别</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pdfData</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pdf数据流（BASE64)</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> printTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告打印时间</span></td><td key=5></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> rangeLimit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">rangeLimit 危急值范围 VARCHAR2(100) 可为空</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告日期</span></td><td key=5></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告科室编码</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告科室名称</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDoctorCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告医师ID、文档作者编码、检验报告医师</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDoctorName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告医师姓名</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportFile</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告文件</span></td><td key=5></td></tr><tr key=0-1-37><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultContext</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查结果指标说明</span></td><td key=5></td></tr><tr key=0-1-38><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultNatureCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">定性定量标识编码 Quan定量 Qual定性 QuanPlusQual 定量+定性</span></td><td key=5></td></tr><tr key=0-1-39><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultNatureName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">定性定量标识名称</span></td><td key=5></td></tr><tr key=0-1-40><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultSign</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">resultSign 结果状态符号 VARCHAR2(100) th_test_record_detail.LIS_RESULT</span></td><td key=5></td></tr><tr key=0-1-41><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultStateClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">低于参考值下th_test_record_detail.VALUE_STATE</span></td><td key=5></td></tr><tr key=0-1-42><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultStateClassName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自定义危急值可为空</span></td><td key=5></td></tr><tr key=0-1-43><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">sampleType 样本类型 VARCHAR2(50) 可为空</span></td><td key=5></td></tr><tr key=0-1-44><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sampleid</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">sampleid 样本号 VARCHAR2(50) th_test_record_detail. SAMPLE_ID</span></td><td key=5></td></tr><tr key=0-1-45><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sortNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验项目序号</span></td><td key=5></td></tr><tr key=0-1-46><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主表主键</span></td><td key=5></td></tr><tr key=0-1-47><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验医师工号</span></td><td key=5></td></tr><tr key=0-1-48><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验医师姓名</span></td><td key=5></td></tr><tr key=0-1-49><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitTypeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">1：门诊 2：住院</span></td><td key=5></td></tr><tr key=0-1-50><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">visitedId 患者流水号 VARCHAR2(50) 可为空</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-2">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"alertFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"approverId":</span><span style="color: green;">""</span>,<span style="color: green;">"approverName":</span><span style="color: green;">""</span>,<span style="color: green;">"barcode":</span><span style="color: green;">""</span>,<span style="color: green;">"comItemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"comItemName":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"encounterClass":</span><span style="color: green;">""</span>,<span style="color: green;">"hisItemId":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResult":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResultRange":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResultUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionSn":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionename":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionid":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionname":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemEname":</span><span style="color: green;">""</span>,<span style="color: green;">"itemMethod":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"machineId":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientSex":</span><span style="color: green;">""</span>,<span style="color: green;">"pdfData":</span><span style="color: green;">""</span>,<span style="color: green;">"printTime":</span><span style="color: green;">""</span>,<span style="color: green;">"rangeLimit":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDate":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDoctorCode":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDoctorName":</span><span style="color: green;">""</span>,<span style="color: green;">"reportFile":</span><span style="color: green;">""</span>,<span style="color: green;">"resultContext":</span><span style="color: green;">""</span>,<span style="color: green;">"resultNatureCode":</span><span style="color: green;">""</span>,<span style="color: green;">"resultNatureName":</span><span style="color: green;">""</span>,<span style="color: green;">"resultSign":</span><span style="color: green;">""</span>,<span style="color: green;">"resultStateClass":</span><span style="color: green;">""</span>,<span style="color: green;">"resultStateClassName":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleType":</span><span style="color: green;">""</span>,<span style="color: green;">"sampleid":</span><span style="color: green;">""</span>,<span style="color: green;">"sortNo":</span><span style="color: green;">""</span>,<span style="color: green;">"testCode":</span><span style="color: green;">""</span>,<span style="color: green;">"testDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"testDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"visitTypeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="113u3010rd113s238u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544a0a3ca20id3d113u3010rd113s238u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544a3e203ca3e">1.1.3【RD1.1.3S238】查询患者Lis微生物报告
<a id=1.1.3【RD1.1.3S238】查询患者Lis微生物报告> </a></h2>
<p></p>
<h3 id="-7">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/microbiologica/detail</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-8">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>branchCode</td>
<td>否</td>
<td></td>
<td>院区编码</td>
</tr>
<tr>
<td>patientId</td>
<td>是</td>
<td></td>
<td>患者ID</td>
</tr>
<tr>
<td>visitedId</td>
<td>否</td>
<td></td>
<td>就诊ID 门诊患者传门诊流水号,住院患者传住院院流水号</td>
</tr>
<tr>
<td>startTime</td>
<td>否</td>
<td></td>
<td>开始时间 yyyy/MM/dd HH24:mi:ss</td>
</tr>
<tr>
<td>endTime</td>
<td>否</td>
<td></td>
<td>结束时间 yyyy/MM/dd HH24:mi:ss</td>
</tr>
</tbody>
</table>
<h3 id="-9">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> barCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">条形码 VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> criticalFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">危急值标识 CHAR(1)</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugSensitiveCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告流水号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> encounterClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">业务来源 CHAR(2)</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主键id VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构英文名称 VARCHAR2(200)</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本级机构 VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构名称 VARCHAR2(200)</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验项目编码 VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验项目名称 VARCHAR2(100)</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号 VARCHAR2(32)</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者ID VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告时间 DATE</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportRemark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告备注 VARCHAR2(200)</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告类型 CHAR(1)</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resourceFile</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病历URL、PDF VARCHAR2(4000)</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结果类型 CHAR(1)</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specimenName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标本名称 VARCHAR2(100)</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者流水号 VARCHAR2(50)</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-3">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"barCode":</span><span style="color: green;">""</span>,<span style="color: green;">"criticalFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"drugSensitiveCode":</span><span style="color: green;">""</span>,<span style="color: green;">"encounterClass":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionEname":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionId":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionName":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDate":</span><span style="color: green;">""</span>,<span style="color: green;">"reportRemark":</span><span style="color: green;">""</span>,<span style="color: green;">"reportType":</span><span style="color: green;">""</span>,<span style="color: green;">"resourceFile":</span><span style="color: green;">""</span>,<span style="color: green;">"resultType":</span><span style="color: green;">""</span>,<span style="color: green;">"specimenName":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="114u3010rd114s239u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544au660eu7ec60a3ca20id3d114u3010rd114s239u3011u67e5u8be2u60a3u8005lisu5faeu751fu7269u62a5u544au660eu7ec63e203ca3e">1.1.4【RD1.1.4S239】查询患者Lis微生物报告明细
<a id=1.1.4【RD1.1.4S239】查询患者Lis微生物报告明细> </a></h2>
<p></p>
<h3 id="-10">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/microbiologica/result</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong></p>
<h3 id="-11">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> drugSensitiveCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> reportId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告序列号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"drugSensitiveCode":"","reportId":""}</pre>
<h3 id="-12">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> antiCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">抗生素代码 VARCHAR2(50) ANTIBIOTIC_CODE</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> antiName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">抗生素名称 VARCHAR2(100) ANTIBIOTIC_NAME</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bacterialNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">细菌编号 VARCHAR2(50) BACTERIAL_NO</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bacterialStrainCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检出细菌代码 VARCHAR2(50) BACTERIAL_STRAIN_CODE</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bacterialStrainComment</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">评语 VARCHAR2(2000) BACTERIAL_STRAIN_COMMENT</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bacterialStrainName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检出细菌名称 VARCHAR2(100) BACTERIAL_STRAIN_NAME</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bcAntiReamrk</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验备注 VARCHAR2(200) 每一组菌株与抗生素验填写备注 BC_AN_REAMRK</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> colonyCount</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">菌落计数 NUMBER(14) COLONY_COUNT</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> colonyCountUnitCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">菌落计数单位代码 VARCHAR2(50) 为空</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> colonyCountUnitName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">菌落计数单位 VARCHAR2(50) COLONY_COUNT_UNIT</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cultureNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">试验序号 VARCHAR2(50) 同一个细菌可能会做多次实验 th_drug_sensitie_detail. TEST_NO</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> encounterClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">业务来源 CHAR(2) 为空</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> germsRemark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">细菌备注 VARCHAR2(200) 【冗余】(每种细菌相同备注) GERMS_REMARK</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> germsTypeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">细菌类型编码 VARCHAR2(50) GERMS_TYPE_CODE</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> germsTypeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">细菌类型 VARCHAR2(100) GERMS_TYPE_NAME</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主键id VARCHAR2(50) th_drug_sensitie_detail. ID</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionMethod</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验方法 VARCHAR2(50) KB、MIC TEST_METHOD</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResult</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验结果 VARCHAR2(100) TEST_RESULT</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResultRange</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">中介值 VARCHAR2(500) RESULT_RANG</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inspectionResultUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验结果单位 VARCHAR2(10) 可为空</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">本级机构 VARCHAR2(50) 集团内统一时，为空</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构名称 VARCHAR2(200)  为空</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionename</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机构英文名称 VARCHAR2(200)  为空</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号 VARCHAR2(32) 可为空</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者ID VARCHAR2(50) HIS5.0为CARD_NO th_drug_sensitie_detail. PATIENT_ID</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> printTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告打印时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportContent</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">短报告内容 VARCHAR2(4000)</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告科室编码</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告科室名称</span></td><td key=5></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDoctorCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告医师ID、文档作者编码、检验报告医师</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDoctorName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报告医师姓名</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> requestDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验申请科室编码</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> requestDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验申请科室名称</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sensitivity</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药敏性简码 CHAR(1) (S/R/I),R表示耐药,S-敏感,I-中度敏感  SENSITIVITY</span></td><td key=5></td></tr><tr key=0-1-37><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sensitivityName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药敏性名称 VARCHAR2(100) R表示耐药,S-敏感,I-中度敏感 SENSITIVITY</span></td><td key=5></td></tr><tr key=0-1-38><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验医师工号</span></td><td key=5></td></tr><tr key=0-1-39><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> testDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检验医师姓名</span></td><td key=5></td></tr><tr key=0-1-40><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者流水号 VARCHAR2(50) th_drug_sensitie_detail. TREATMENT_CODE</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-4">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"antiCode":</span><span style="color: green;">""</span>,<span style="color: green;">"antiName":</span><span style="color: green;">""</span>,<span style="color: green;">"bacterialNo":</span><span style="color: green;">""</span>,<span style="color: green;">"bacterialStrainCode":</span><span style="color: green;">""</span>,<span style="color: green;">"bacterialStrainComment":</span><span style="color: green;">""</span>,<span style="color: green;">"bacterialStrainName":</span><span style="color: green;">""</span>,<span style="color: green;">"bcAntiReamrk":</span><span style="color: green;">""</span>,<span style="color: green;">"colonyCount":</span><span style="color: green;">0</span>,<span style="color: green;">"colonyCountUnitCode":</span><span style="color: green;">""</span>,<span style="color: green;">"colonyCountUnitName":</span><span style="color: green;">""</span>,<span style="color: green;">"cultureNo":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"encounterClass":</span><span style="color: green;">""</span>,<span style="color: green;">"germsRemark":</span><span style="color: green;">""</span>,<span style="color: green;">"germsTypeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"germsTypeName":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionMethod":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResult":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResultRange":</span><span style="color: green;">""</span>,<span style="color: green;">"inspectionResultUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionId":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionName":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionename":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"printTime":</span><span style="color: green;">""</span>,<span style="color: green;">"reportContent":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDate":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDoctorCode":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDoctorName":</span><span style="color: green;">""</span>,<span style="color: green;">"requestDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"requestDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"sensitivity":</span><span style="color: green;">""</span>,<span style="color: green;">"sensitivityName":</span><span style="color: green;">""</span>,<span style="color: green;">"testDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"testDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h1 id="1220pacs">1.2 pacs</h1>
<p></p>
<h2 id="121u3010rd121s240u3011u67e5u8be2pacsu68c0u67e5u62a5u544a28cdru5e93290a3ca20id3d121u3010rd121s240u3011u67e5u8be2pacsu68c0u67e5u62a5u544a28cdru5e93293e203ca3e">1.2.1【RD1.2.1S240】查询Pacs检查报告(Cdr库)
<a id=1.2.1【RD1.2.1S240】查询Pacs检查报告(Cdr库)> </a></h2>
<p></p>
<h3 id="-13">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/pacs/detail</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-14">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>branchCode</td>
<td>否</td>
<td></td>
<td>院区编码</td>
</tr>
<tr>
<td>patientId</td>
<td>否</td>
<td></td>
<td>患者ID</td>
</tr>
<tr>
<td>visitedId</td>
<td>否</td>
<td></td>
<td>就诊ID 门诊患者传门诊流水号,住院患者传住院院流水号</td>
</tr>
<tr>
<td>examTypeClass</td>
<td>否</td>
<td></td>
<td>检查类型</td>
</tr>
</tbody>
</table>
<h3 id="-15">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> applyDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查申请科室名称</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> applyDeptNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查申请科室编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> checkDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> comNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">申请单号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> confirmDrCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">审核医师编码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> confirmDrName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">confirmDrName 审核医生姓名   为空</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> diagnosis</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">diagnosis 诊断结果 VARCHAR2(500) CHECK_DIAG</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> encounterClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">encounterClass 业务来源 CHAR(2) 为空</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examBodysite</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examBodysite 检查部位 VARCHAR2(500) CHECK_PART</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examDate 检查时间 DATE 出报告的时间 CHECK_DATE</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查医师编码</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查医师签名</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examName 项目名称 VARCHAR2(500) CHECK_NAME</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examObservation</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examObservation 检查所见 VARCHAR2(3000) CHECK_VIEW</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examResult</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examResult 检查结果 VARCHAR2(500) CHECK_RESULT</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examTechCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查技师编码</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examTechName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查技师姓名</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examTypeClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examTypeClass 检查项目分类【E】 CHAR(2) CHECK_TYPE 以下信息每项目存在差异 1 普通X光机 02 CR 03 CT 04 MRI 05 超声 06 内窥镜 07 DR 08 DSA 09 数字胃肠 10 彩色多普勒 11 PETMR 12: 病理 13 ECT 14 PETCT 15: 骨髓细胞镜 16 临床遗传分子镜 17 肺功能 18 心功能 19 神经功能 20 MG 21 牙片机 22 肌电 23脑电 24听力</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> examename</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">examEName 项目英文名称 VARCHAR2(500) 为空</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> icdCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">icdCode 诊断代码 VARCHAR2(100) 为空</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionEname 机构英文名称 VARCHAR2(200) 为空</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionId 本级机构 VARCHAR2(50) 集团内统一时，为空</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionName 机构名称 VARCHAR2(200) 为空</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> machinetypename</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">设备名称</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医生编码</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医生名称</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">ordSn 医嘱流水号 VARCHAR2(32) ORDER_ID</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">patientId 患者ID VARCHAR2(50) 5.0为CARD_NO PATIENT_ID</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">patientName 患者姓名 VARCHAR2(50) 5.0为NAME</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pdfData</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pdf数据流（BASE64)</span></td><td key=5></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查报告科室名称</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查报告医师编码</span></td><td key=5></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">reportDocName 报告医生姓名   检查医生 为空</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportExamSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">reportExamSn 报告流水号 VARCHAR2(50) 为空</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportImageUrl</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">reportImageUrl 报告图像位置   FTP服务地址/Url(互联网医院) 为空</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportLookTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">reportLookTime 审核后多久患者可以看报告   为空</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportadvice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">建议</span></td><td key=5></td></tr><tr key=0-1-37><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resourceFile</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">resourceFile 病历url、pdf VARCHAR2(4000) REPORT_URL</span></td><td key=5></td></tr><tr key=0-1-38><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resultContext</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查结果指标说明</span></td><td key=5></td></tr><tr key=0-1-39><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> termCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">termCode 项目编码 VARCHAR2(50) CHECK_ITEM_CODE</span></td><td key=5></td></tr><tr key=0-1-40><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">visitedId 就诊流水号 VARCHAR2(50) TREATMENT_CODE</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-5">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"applyDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"applyDeptNo":</span><span style="color: green;">""</span>,<span style="color: green;">"checkDate":</span><span style="color: green;">""</span>,<span style="color: green;">"comNo":</span><span style="color: green;">""</span>,<span style="color: green;">"confirmDrCode":</span><span style="color: green;">""</span>,<span style="color: green;">"confirmDrName":</span><span style="color: green;">""</span>,<span style="color: green;">"diagnosis":</span><span style="color: green;">""</span>,<span style="color: green;">"encounterClass":</span><span style="color: green;">""</span>,<span style="color: green;">"examBodysite":</span><span style="color: green;">""</span>,<span style="color: green;">"examDate":</span><span style="color: green;">""</span>,<span style="color: green;">"examDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"examDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"examName":</span><span style="color: green;">""</span>,<span style="color: green;">"examObservation":</span><span style="color: green;">""</span>,<span style="color: green;">"examResult":</span><span style="color: green;">""</span>,<span style="color: green;">"examTechCode":</span><span style="color: green;">""</span>,<span style="color: green;">"examTechName":</span><span style="color: green;">""</span>,<span style="color: green;">"examTypeClass":</span><span style="color: green;">""</span>,<span style="color: green;">"examename":</span><span style="color: green;">""</span>,<span style="color: green;">"icdCode":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionEname":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionId":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionName":</span><span style="color: green;">""</span>,<span style="color: green;">"machinetypename":</span><span style="color: green;">""</span>,<span style="color: green;">"moDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"moDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"pdfData":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"reportExamSn":</span><span style="color: green;">""</span>,<span style="color: green;">"reportImageUrl":</span><span style="color: green;">""</span>,<span style="color: green;">"reportLookTime":</span><span style="color: green;">""</span>,<span style="color: green;">"reportadvice":</span><span style="color: green;">""</span>,<span style="color: green;">"resourceFile":</span><span style="color: green;">""</span>,<span style="color: green;">"resultContext":</span><span style="color: green;">""</span>,<span style="color: green;">"termCode":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="122u3010rd122s241u3011u67e5u8be2u60a3u8005pacsu75c5u7406u62a5u544a0a3ca20id3d122u3010rd122s241u3011u67e5u8be2u60a3u8005pacsu75c5u7406u62a5u544a3e203ca3e">1.2.2【RD1.2.2S241】查询患者Pacs病理报告
<a id=1.2.2【RD1.2.2S241】查询患者Pacs病理报告> </a></h2>
<p></p>
<h3 id="-16">基本信息</h3>
<p><strong>Path：</strong> /api/v1/cdr/pathology/detail</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong></p>
<h3 id="-17">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>branchCode</td>
<td>否</td>
<td></td>
<td>院区编码</td>
</tr>
<tr>
<td>patientId</td>
<td>否</td>
<td></td>
<td>患者ID</td>
</tr>
<tr>
<td>visitedId</td>
<td>否</td>
<td></td>
<td>就诊ID 门诊患者传门诊流水号,住院患者传住院院流水号</td>
</tr>
<tr>
<td>examTypeClass</td>
<td>否</td>
<td></td>
<td>检查类型</td>
</tr>
</tbody>
</table>
<h3 id="-18">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> diagnosis</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">diagnosis 诊断结果 VARCHAR2(500) PATHOLOGICAL_DIAGNOSIS</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> encounterClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">encounterClass 业务来源 CHAR(2) 为空</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> icdCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">icdCode 诊断代码 VARCHAR2(100)</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">id 主键id VARCHAR2(32)  ID</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionEname 机构英文名称 VARCHAR2(200) 为空</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionId 本级机构 VARCHAR2(50) 集团内统一时,为空</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> institutionName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">institutionName 机构名称 VARCHAR2(200) 为空</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemCode 项目编码 VARCHAR2(50) PROCEDURE_CODE</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">itemName 项目名称 VARCHAR2(50) PROCEDURE_NAME</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> microscopicExam</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">microscopicExam 镜下所见 VARCHAR2(500) MICROSCOPIC_VIEW</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nakedEyeObservation</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">nakedEyeObservation 肉眼所见 VARCHAR2(500) NAKED_EYE 描述</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ordSn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">ordSn 医嘱流水号 VARCHAR2(32) ORDERID</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pathologyId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pathologyId 病理报告 VARCHAR2(50) DOCUMENT_CODE</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pathologyName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pathologyName 病理报告名 VARCHAR2(100) PATHOLOGY_NAME</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pathologyPosition</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pathologyPosition 送检部位、样品 VARCHAR2(100) POSITION</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pathologyTypeClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pathologyTypeClass 报告类型【E】 CHAR(2) 为空 冰冻、切片</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">patientId 患者ID VARCHAR2(50) 5.0为CARD_NO PATIENT_ID</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reportDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">reportDate 报告时间 DATE REPORT_DATE</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> resourceFile</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">resourceFile 病理url、pdf VARCHAR2(4000) FILE_DATA</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> visitedId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">visitedId 就诊流水号 VARCHAR2(50) TREATMENT_CODE 门诊流水号,住院流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-6">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"diagnosis":</span><span style="color: green;">""</span>,<span style="color: green;">"encounterClass":</span><span style="color: green;">""</span>,<span style="color: green;">"icdCode":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionEname":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionId":</span><span style="color: green;">""</span>,<span style="color: green;">"institutionName":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"microscopicExam":</span><span style="color: green;">""</span>,<span style="color: green;">"nakedEyeObservation":</span><span style="color: green;">""</span>,<span style="color: green;">"ordSn":</span><span style="color: green;">""</span>,<span style="color: green;">"pathologyId":</span><span style="color: green;">""</span>,<span style="color: green;">"pathologyName":</span><span style="color: green;">""</span>,<span style="color: green;">"pathologyPosition":</span><span style="color: green;">""</span>,<span style="color: green;">"pathologyTypeClass":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"reportDate":</span><span style="color: green;">""</span>,<span style="color: green;">"resourceFile":</span><span style="color: green;">""</span>,<span style="color: green;">"visitedId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
            <footer class="m-footer">
              <p>Build by <a href="https://ymfe.org/">YMFE</a>.</p>
            </footer>
          </div>
        </div>
      </body>
      </html>
      