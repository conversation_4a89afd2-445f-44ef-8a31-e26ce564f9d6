# request

```bash
curl -s "http://127.0.0.1:10011/roc/curr-web/api/v1/curr/unpharmaceutical/undrug/eisaiOrDiagnose/query?vagueName=%E4%B8%80%E6%AC%A1%E6%80%A7%E9%87%87%E8%A1%80%E5%99%A8" \
     -H 'domain: NEU_HZFW' \
     -H 'key: f69897d6-d8a7-4c84-ac86-cca3b463b249' \
     --fail | jq . | pbcopy
```

# response

```json
{
    "code": 200,
    "msg": "操作成功！",
    "data": [
        {
            "name": "一次性采血器（普通）",
            "price": "2",
            "specs": null,
            "unit": "次",
            "producerCode": null,
            "producerName": null,
            "identifyKey": "F00000080082",
            "execDeptCode": null,
            "execDeptName": null,
            "sysClass": "U",
            "spellCode": "YCXCXQPT",
            "unitflag": "1",
            "gbCode": "F00000080082",
            "defaultSample": null,
            "validState": "1"
        },
        {
            "name": "一次性采血器(抗凝)",
            "price": "0.98",
            "specs": "国产|1.8mL",
            "unit": "支",
            "producerCode": null,
            "producerName": null,
            "identifyKey": "F00000047480",
            "execDeptCode": null,
            "execDeptName": null,
            "sysClass": "U",
            "spellCode": "YCXCXQKN",
            "unitflag": "0",
            "gbCode": "F00000047480",
            "defaultSample": null,
            "validState": "1"
        },
        {
            "name": "一次性采血器(普通)",
            "price": "0.78",
            "specs": "国产|普通",
            "unit": "套",
            "producerCode": null,
            "producerName": null,
            "identifyKey": "F00000047484",
            "execDeptCode": null,
            "execDeptName": null,
            "sysClass": "U",
            "spellCode": "YCXCXQPT",
            "unitflag": "0",
            "gbCode": "F00000047484",
            "defaultSample": null,
            "validState": "1"
        }
    ]
}
```