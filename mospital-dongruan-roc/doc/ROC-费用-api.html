<!DOCTYPE html>
      <html>
      <head>
      <title>fee-service</title>
      <meta charset="utf-8" />
      <style>@charset "UTF-8";
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.2);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  font-size: 13px;
  line-height: 25px;
  color: #393838;
  position: relative;
}

table {
  margin: 10px 0 15px 0;
  border-collapse: collapse;
}

td,
th {
  border: 1px solid #ddd;
  padding: 3px 10px;
}

th {
  padding: 5px 10px;
}

a, a:link, a:visited {
  color: #34495e;
  text-decoration: none;
}

a:hover, a:focus {
  color: #59d69d;
  text-decoration: none;
}

a img {
  border: none;
}

p {
  padding-left: 10px;
  margin-bottom: 9px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #404040;
  line-height: 36px;
}

h1 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
  line-height: 50px;
}

h2 {
  font-size: 28px;
  padding-top: 10px;
  padding-bottom: 10px;
}

h3 {
  clear: both;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  border-left: 3px solid #59d69d;
  padding-left: 8px;
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 13px;
}

hr {
  margin: 0 0 19px;
  border: 0;
  border-bottom: 1px solid #ccc;
}

blockquote {
  padding: 13px 13px 21px 15px;
  margin-bottom: 18px;
  font-family: georgia, serif;
  font-style: italic;
}

blockquote:before {
  font-size: 40px;
  margin-left: -10px;
  font-family: georgia, serif;
  color: #eee;
}

blockquote p {
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  margin-bottom: 0;
  font-style: italic;
}

code,
pre {
  font-family: Monaco, Andale Mono, Courier New, monospace;
}

code {
  background-color: #fee9cc;
  color: rgba(0, 0, 0, 0.75);
  padding: 1px 3px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

pre {
  display: block;
  padding: 14px;
  margin: 0 0 18px;
  line-height: 16px;
  font-size: 11px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f6f6f6;
}

pre code {
  background-color: #f6f6f6;
  color: #737373;
  font-size: 11px;
  padding: 0;
}

sup {
  font-size: 0.83em;
  vertical-align: super;
  line-height: 0;
}

* {
  -webkit-print-color-adjust: exact;
}

@media print {
  body,
  code,
  pre code,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black;
  }

  table,
  pre {
    page-break-inside: avoid;
  }
}
html,
body {
  height: 100%;
}

.table-of-contents {
  position: fixed;
  top: 61px;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  width: 260px;
}

.table-of-contents > ul > li > a {
  font-size: 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.table-of-contents ul {
  overflow: auto;
  margin: 0px;
  height: 100%;
  padding: 0px 0px;
  box-sizing: border-box;
  list-style-type: none;
}

.table-of-contents ul li {
  padding-left: 20px;
}

.table-of-contents a {
  padding: 2px 0px;
  display: block;
  text-decoration: none;
}

.content-right {
  max-width: 700px;
  margin-left: 290px;
  padding-left: 70px;
  flex-grow: 1;
}
.content-right h2:target {
  padding-top: 80px;
}

body > p {
  margin-left: 30px;
}

body > table {
  margin-left: 30px;
}

body > pre {
  margin-left: 30px;
}

.curProject {
  position: fixed;
  top: 20px;
  font-size: 25px;
  color: black;
  margin-left: -240px;
  width: 240px;
  padding: 5px;
  line-height: 25px;
  box-sizing: border-box;
}

.g-doc {
  margin-top: 56px;
  padding-top: 24px;
  display: flex;
}

.curproject-name {
  font-size: 42px;
}

.m-header {
  background: #32363a;
  height: 56px;
  line-height: 56px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
}
.m-header .title {
  font-size: 22px;
  color: #fff;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  margin-left: 16px;
  padding: 0;
  line-height: 56px;
  border: none;
}
.m-header .nav {
  color: #fff;
  font-size: 16px;
  position: absolute;
  right: 32px;
  top: 0;
}
.m-header .nav a {
  color: #fff;
  margin-left: 16px;
  padding: 8px;
  transition: color .2s;
}
.m-header .nav a:hover {
  color: #59d69d;
}

.m-footer {
  border-top: 1px solid #ddd;
  padding-top: 16px;
  padding-bottom: 16px;
}

/*# sourceMappingURL=defaultTheme.css.map */
</style>
      </head>
      <body>
        <div class="m-header">
          <a href="#" style="display: inherit;"><svg class="svg" width="32px" height="32px" viewBox="0 0 64 64" version="1.1"><title>Icon</title><desc>Created with Sketch.</desc><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1"><stop stop-color="#FFFFFF" offset="0%"></stop><stop stop-color="#F2F2F2" offset="100%"></stop></linearGradient><circle id="path-2" cx="31.9988602" cy="31.9988602" r="2.92886048"></circle><filter x="-85.4%" y="-68.3%" width="270.7%" height="270.7%" filterUnits="objectBoundingBox" id="filter-3"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.159703351 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter></defs><g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="大屏幕"><g id="Icon"><circle id="Oval-1" fill="url(#linearGradient-1)" cx="32" cy="32" r="32"></circle><path d="M36.7078009,31.8054514 L36.7078009,51.7110548 C36.7078009,54.2844537 34.6258634,56.3695395 32.0579205,56.3695395 C29.4899777,56.3695395 27.4099998,54.0704461 27.4099998,51.7941246 L27.4099998,31.8061972 C27.4099998,29.528395 29.4909575,27.218453 32.0589004,27.230043 C34.6268432,27.241633 36.7078009,29.528395 36.7078009,31.8054514 Z" id="blue" fill="#2359F1" fill-rule="nonzero"></path><path d="M45.2586091,17.1026914 C45.2586091,17.1026914 45.5657231,34.0524383 45.2345291,37.01141 C44.9033351,39.9703817 43.1767091,41.6667796 40.6088126,41.6667796 C38.040916,41.6667796 35.9609757,39.3676862 35.9609757,37.0913646 L35.9609757,17.1034372 C35.9609757,14.825635 38.0418959,12.515693 40.6097924,12.527283 C43.177689,12.538873 45.2586091,14.825635 45.2586091,17.1026914 Z" id="green" fill="#57CF27" fill-rule="nonzero" transform="translate(40.674608, 27.097010) rotate(60.000000) translate(-40.674608, -27.097010) "></path><path d="M28.0410158,17.0465598 L28.0410158,36.9521632 C28.0410158,39.525562 25.9591158,41.6106479 23.3912193,41.6106479 C20.8233227,41.6106479 18.7433824,39.3115545 18.7433824,37.035233 L18.7433824,17.0473055 C18.7433824,14.7695034 20.8243026,12.4595614 23.3921991,12.4711513 C25.9600956,12.4827413 28.0410158,14.7695034 28.0410158,17.0465598 Z" id="red" fill="#FF561B" fill-rule="nonzero" transform="translate(23.392199, 27.040878) rotate(-60.000000) translate(-23.392199, -27.040878) "></path><g id="inner-round"><use fill="black" fill-opacity="1" filter="url(#filter-3)" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use><use fill="#F7F7F7" fill-rule="evenodd" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use></g></g></g></g></svg></a>
          <a href="#"><h1 class="title">YAPI 接口文档</h1></a>
          <div class="nav">
            <a href="https://hellosean1025.github.io/yapi/">YApi</a>
          </div>
        </div>
        <div class="g-doc">
          <div class="table-of-contents"><ul><li><a href="#21u67e5u8be2u7c7b">2.1查询类</a><ul><li><a href="#211u3010rf211s125u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u660eu7ec6u5217u88680a3ca20id3d211u3010rf211s125u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u660eu7ec6u5217u88683e203ca3e">2.1.1【RF2.1.1S125】查询门诊患者费用明细列表
<a id=2.1.1【RF2.1.1S125】查询门诊患者费用明细列表> </a></a></li><li><a href="#212u3010rf212s126u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u5217u88680a3ca20id3d212u3010rf212s126u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u5217u88683e203ca3e">2.1.2【RF2.1.2S126】查询门诊患者费用支付状态列表
<a id=2.1.2【RF2.1.2S126】查询门诊患者费用支付状态列表> </a></a></li><li><a href="#213u3010rf213s127u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u4e0eu660eu7ec60a3ca20id3d213u3010rf213s127u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u4e0eu660eu7ec63e203ca3e">2.1.3【RF2.1.3S127】查询门诊患者费用支付状态与明细
<a id=2.1.3【RF2.1.3S127】查询门诊患者费用支付状态与明细> </a></a></li><li><a href="#215u3010rf215s129u3011u67e5u8be2u4f4fu9662u60a3u8005u65e5u8d26u5355u4fe1u606f0a3ca20id3d215u3010rf215s129u3011u67e5u8be2u4f4fu9662u60a3u8005u65e5u8d26u5355u4fe1u606f3e203ca3e">2.1.5【RF2.1.5S129】查询住院患者日账单信息
<a id=2.1.5【RF2.1.5S129】查询住院患者日账单信息> </a></a></li><li><a href="#216u3010rf216s130u3011u67e5u8be2u4f4fu9662u60a3u8005u9884u4ea4u91d1u4fe1u606f0a3ca20id3d216u3010rf216s130u3011u67e5u8be2u4f4fu9662u60a3u8005u9884u4ea4u91d1u4fe1u606f3e203ca3e">2.1.6【RF2.1.6S130】查询住院患者预交金信息
<a id=2.1.6【RF2.1.6S130】查询住院患者预交金信息> </a></a></li><li><a href="#217u3010rf217s131u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u5206u7c7bu6c47u603buff090a3ca20id3d217u3010rf217s131u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u5206u7c7bu6c47u603buff093e203ca3e">2.1.7【RF2.1.7S131】查询住院费用（按分类汇总）
<a id=2.1.7【RF2.1.7S131】查询住院费用（按分类汇总）> </a></a></li><li><a href="#218u3010rf218s132u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u65e5u671fu6c47u603buff090a3ca20id3d218u3010rf218s132u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u65e5u671fu6c47u603buff093e203ca3e">2.1.8【RF2.1.8S132】查询住院费用（按日期汇总）
<a id=2.1.8【RF2.1.8S132】查询住院费用（按日期汇总）> </a></a></li><li><a href="#219u3010rf219s212u3011u67e5u8be2u533bu5631u72b6u6001u548cu53efu9000u8d39u6570u91cf0a3ca20id3d219u3010rf219s212u3011u67e5u8be2u533bu5631u72b6u6001u548cu53efu9000u8d39u6570u91cf3e203ca3e">2.1.9【RF2.1.9S212】查询医嘱状态和可退费数量
<a id=2.1.9【RF2.1.9S212】查询医嘱状态和可退费数量> </a></a></li><li><a href="#2110u3010rf2110s210u3011u67e5u8be2u5168u9662u95e8u8bcau7f34u8d39u660e0a3ca20id3d2110u3010rf2110s210u3011u67e5u8be2u5168u9662u95e8u8bcau7f34u8d39u660e3e203ca3e">2.1.10【RF2.1.10S210】查询全院门诊缴费明
<a id=2.1.10【RF2.1.10S210】查询全院门诊缴费明> </a></a></li><li><a href="#u3010rf2212s230u3011u67e5u8be2u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f0a3ca20id3du3010rf2212s230u3011u67e5u8be2u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f3e203ca3e">【RF2.2.12S230】查询住院患者药品费用信息
<a id=【RF2.2.12S230】查询住院患者药品费用信息> </a></a></li><li><a href="#u3010rf2211s229u3011u67e5u8be2u4f4fu9662u60a3u8005u975eu836fu54c1u8d39u7528u4fe1u606f0a3ca20id3du3010rf2211s229u3011u67e5u8be2u4f4fu9662u60a3u8005u975eu836fu54c1u8d39u7528u4fe1u606f3e203ca3e">【RF2.2.11S229】查询住院患者非药品费用信息
<a id=【RF2.2.11S229】查询住院患者非药品费用信息> </a></a></li></ul></li><li><a href="#22u64cdu4f5cu7c7b">2.2操作类</a><ul><li><a href="#221u3010rf221u133u3011u7f34u7eb3u4f4fu9662u9884u4ea4u91d10a3ca20id3d221u3010rf221u133u3011u7f34u7eb3u4f4fu9662u9884u4ea4u91d13e203ca3e">2.2.1【RF2.2.1U133】缴纳住院预交金
<a id=2.2.1【RF2.2.1U133】缴纳住院预交金> </a></a></li><li><a href="#222u3010rf222u190u3011u4fddu5b58u95e8u8bcau8d39u7528u4fe1u606f0a3ca20id3d222u3010rf222u190u3011u4fddu5b58u95e8u8bcau8d39u7528u4fe1u606f3e203ca3e">2.2.2【RF2.2.2U190】保存门诊费用信息
<a id=2.2.2【RF2.2.2U190】保存门诊费用信息> </a></a></li><li><a href="#223u3010rf223u191u3011u5220u9664u95e8u8bcau8d39u7528u4fe1u606f0a3ca20id3d223u3010rf223u191u3011u5220u9664u95e8u8bcau8d39u7528u4fe1u606f3e203ca3e">2.2.3【RF2.2.3U191】删除门诊费用信息
<a id=2.2.3【RF2.2.3U191】删除门诊费用信息> </a></a></li><li><a href="#224u3010rf224s199u3011u67e5u5168u9662u4f4fu9662u9884u4ea4u91d1u60c5u51b5u6c47u603b0a3ca20id3d224u3010rf224s199u3011u67e5u5168u9662u4f4fu9662u9884u4ea4u91d1u60c5u51b5u6c47u603b3e203ca3e">2.2.4【RF2.2.4S199】查全院住院预交金情况汇总
<a id=2.2.4【RF2.2.4S199】查全院住院预交金情况汇总> </a></a></li><li><a href="#225u3010rf225s200u3011u67e5u8be2u5168u9662u95e8u8bcau6536u8d39u60c5u51b5u6c47u603b0a3ca20id3d225u3010rf225s200u3011u67e5u8be2u5168u9662u95e8u8bcau6536u8d39u60c5u51b5u6c47u603b3e203ca3e">2.2.5【RF2.2.5S200】查询全院门诊收费情况汇总
<a id=2.2.5【RF2.2.5S200】查询全院门诊收费情况汇总> </a></a></li><li><a href="#226u3010rf226s201u3011u67e5u8be2u5168u9662u6302u53f7u8bb0u5f550a3ca20id3d226u3010rf226s201u3011u67e5u8be2u5168u9662u6302u53f7u8bb0u5f553e203ca3e">2.2.6【RF2.2.6S201】查询全院挂号记录
<a id=2.2.6【RF2.2.6S201】查询全院挂号记录> </a></a></li><li><a href="#227u3010rf227i205u3011u95e8u8bcau9000u8d39u7533u8bf70a3ca20id3d227u3010rf227i205u3011u95e8u8bcau9000u8d39u7533u8bf73e203ca3e">2.2.7【RF2.2.7I205】门诊退费申请
<a id=2.2.7【RF2.2.7I205】门诊退费申请> </a></a></li><li><a href="#228u3010rf228i214u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u6279u8d39u63a5u53e3290a3ca20id3d228u3010rf228i214u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u6279u8d39u63a5u53e3293e203ca3e">2.2.8【RF2.2.8I214】保存住院患者费用信息(住院辅材批费接口)
<a id=2.2.8【RF2.2.8I214】保存住院患者费用信息(住院辅材批费接口)> </a></a></li><li><a href="#229u3010rf229i217u3011u95e8u8bcau6536u8d390a3ca20id3d229u3010rf229i217u3011u95e8u8bcau6536u8d393e203ca3e">2.2.9【RF2.2.9I217】门诊收费
<a id=2.2.9【RF2.2.9I217】门诊收费> </a></a></li><li><a href="#2210u3010rf2210i222u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4fddu5b58u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f290a3ca20id3d2210u3010rf2210i222u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4fddu5b58u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f293e203ca3e">2.2.10【RF2.2.10I222】保存住院患者费用信息(保存住院患者药品费用信息)
<a id=2.2.10【RF2.2.10I222】保存住院患者费用信息(保存住院患者药品费用信息)> </a></a></li><li><a href="#2211u3010rf2211u057u3011u9662u5185u5361u5145u503c0a3ca20id3d2211u3010rf2211u057u3011u9662u5185u5361u5145u503c3e203ca3e">2.2.11【RF2.2.11U057】院内卡充值
<a id=2.2.11【RF2.2.11U057】院内卡充值> </a></a></li><li><a href="#2212u3010rf2212d232u3011u5220u9664u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u53d6u6d88u6279u8d39u63a5u53e3290a3ca20id3d2212u3010rf2212d232u3011u5220u9664u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u53d6u6d88u6279u8d39u63a5u53e3293e203ca3e">2.2.12【RF2.2.12D232】删除住院患者费用信息(住院辅材取消批费接口)
<a id=2.2.12【RF2.2.12D232】删除住院患者费用信息(住院辅材取消批费接口)> </a></a></li><li><a href="#2213u3010rf2213d236u3011u5220u9664u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f0a3ca20id3d2213u3010rf2213d236u3011u5220u9664u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f3e203ca3e">2.2.13【RF2.2.13D236】删除住院患者药品费用信息
<a id=2.2.13【RF2.2.13D236】删除住院患者药品费用信息> </a></a></li><li><a href="#2214u3010rf2214i237u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u975eu836fuff090a3ca20id3d2214u3010rf2214i237u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u975eu836fuff093e203ca3e">2.2.14【RF2.2.14I237】住院患者费用信息保存接口（非药）
<a id=2.2.14【RF2.2.14I237】住院患者费用信息保存接口（非药）> </a></a></li><li><a href="#2215u3010rf2215i238u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u836fu54c1uff090a3ca20id3d2215u3010rf2215i238u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u836fu54c1uff093e203ca3e">2.2.15【RF2.2.15I238】住院患者费用信息保存接口（药品）
<a id=2.2.15【RF2.2.15I238】住院患者费用信息保存接口（药品）> </a></a></li><li><a href="#2216u3010rf2216i239u3011u95e8u8bcau6536u8d39u4fe1u606fu7ed3u7b970a3ca20id3d2216u3010rf2216i239u3011u95e8u8bcau6536u8d39u4fe1u606fu7ed3u7b973e203ca3e">2.2.16【RF2.2.16I239】门诊收费信息结算
<a id=2.2.16【RF2.2.16I239】门诊收费信息结算> </a></a></li></ul></li></ul></div>
          <div id="right" class="content-right">
           <h1 class="curproject-name"> fee-service </h1> 
 费用相关服务接口
<h1 id="21u67e5u8be2u7c7b">2.1查询类</h1>
<p></p>
<h2 id="211u3010rf211s125u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u660eu7ec6u5217u88680a3ca20id3d211u3010rf211s125u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u660eu7ec6u5217u88683e203ca3e">2.1.1【RF2.1.1S125】查询门诊患者费用明细列表
<a id=2.1.1【RF2.1.1S125】查询门诊患者费用明细列表> </a></h2>
<p></p>
<h3 id="">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/detail/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
根据挂号流水号，收费标价，处方号查询</p>
<h3 id="-2">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>clinicCode</td>
<td>否</td>
<td></td>
<td>挂号流水号</td>
</tr>
<tr>
<td>recipeNo</td>
<td>否</td>
<td></td>
<td>处方号</td>
</tr>
<tr>
<td>orderId</td>
<td>否</td>
<td></td>
<td>医嘱id</td>
</tr>
<tr>
<td>payFlag</td>
<td>否</td>
<td></td>
<td>收费标识</td>
</tr>
<tr>
<td>cardNo</td>
<td>否</td>
<td></td>
<td>门诊号</td>
</tr>
<tr>
<td>beginDate</td>
<td>否</td>
<td></td>
<td>收费时间范围From</td>
</tr>
<tr>
<td>endDate</td>
<td>否</td>
<td></td>
<td>收费时间范围To</td>
</tr>
</tbody>
</table>
<h3 id="-3">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptAdress</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室地址</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeList</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">返回交易明细</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-3-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> baseDose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">基本剂量</span></td><td key=5></td></tr><tr key=0-1-3-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> branchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院区编码</span></td><td key=5></td></tr><tr key=0-1-3-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> classCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目分类</span></td><td key=5></td></tr><tr key=0-1-3-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-1-3-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> combNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组合号</span></td><td key=5></td></tr><tr key=0-1-3-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> confirmFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行标记</span></td><td key=5></td></tr><tr key=0-1-3-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-3-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-3-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diseCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">慢病编码</span></td><td key=5></td></tr><tr key=0-1-3-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diseName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">慢病诊断</span></td><td key=5></td></tr><tr key=0-1-3-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> docCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-3-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> docName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生名称</span></td><td key=5></td></tr><tr key=0-1-3-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> doseOnce</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">每次用量</span></td><td key=5></td></tr><tr key=0-1-3-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> doseUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">每次用量单位</span></td><td key=5></td></tr><tr key=0-1-3-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品标记</span></td><td key=5></td></tr><tr key=0-1-3-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptadss</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室地址</span></td><td key=5></td></tr><tr key=0-1-3-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDpcd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-1-3-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDpnm</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室</span></td><td key=5></td></tr><tr key=0-1-3-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用编码</span></td><td key=5></td></tr><tr key=0-1-3-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-3-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用名称</span></td><td key=5></td></tr><tr key=0-1-3-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> frequencyCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">频次编码</span></td><td key=5></td></tr><tr key=0-1-3-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> frequencyName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">频次名称</span></td><td key=5></td></tr><tr key=0-1-3-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> hospitalCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医院编码</span></td><td key=5></td></tr><tr key=0-1-3-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发票号</span></td><td key=5></td></tr><tr key=0-1-3-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-3-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称</span></td><td key=5></td></tr><tr key=0-1-3-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> noBackNum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-3-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费用</span></td><td key=5></td></tr><tr key=0-1-3-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> packageCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">复合项目代码</span></td><td key=5></td></tr><tr key=0-1-3-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位编码</span></td><td key=5></td></tr><tr key=0-1-3-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-3-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payChannel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式</span></td><td key=5></td></tr><tr key=0-1-3-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payChannelName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式名称</span></td><td key=5></td></tr><tr key=0-1-3-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠用</span></td><td key=5></td></tr><tr key=0-1-3-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费标记</span></td><td key=5></td></tr><tr key=0-1-3-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报销用</span></td><td key=5></td></tr><tr key=0-1-3-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5></td></tr><tr key=0-1-3-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方开立时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-3-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-3-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费序列</span></td><td key=5></td></tr><tr key=0-1-3-41><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> regDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-3-42><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> seeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊序号</span></td><td key=5></td></tr><tr key=0-1-3-43><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendTerminalName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药窗口名称</span></td><td key=5></td></tr><tr key=0-1-3-44><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-3-45><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> serialNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-3-46><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> singlePrice</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-3-47><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> spesc</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-3-48><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> state</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目状态</span></td><td key=5></td></tr><tr key=0-1-3-49><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型 交易类型,1正交易，2反交易[5]</span></td><td key=5></td></tr><tr key=0-1-3-50><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-3-51><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> usageCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用法编码</span></td><td key=5></td></tr><tr key=0-1-3-52><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> usageName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用法名称</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总费用</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总费用</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总费用</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"deptAdress":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeList":</span>[{<span style="color: green;">"baseDose":</span><span style="color: green;">""</span>,<span style="color: green;">"branchCode":</span><span style="color: green;">""</span>,<span style="color: green;">"classCode":</span><span style="color: green;">""</span>,<span style="color: green;">"clinicCode":</span><span style="color: green;">""</span>,<span style="color: green;">"combNo":</span><span style="color: green;">""</span>,<span style="color: green;">"confirmFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"diseCode":</span><span style="color: green;">""</span>,<span style="color: green;">"diseName":</span><span style="color: green;">""</span>,<span style="color: green;">"docCode":</span><span style="color: green;">""</span>,<span style="color: green;">"docName":</span><span style="color: green;">""</span>,<span style="color: green;">"doseOnce":</span><span style="color: green;">0</span>,<span style="color: green;">"doseUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"drugFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptadss":</span><span style="color: green;">""</span>,<span style="color: green;">"execDpcd":</span><span style="color: green;">""</span>,<span style="color: green;">"execDpnm":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeName":</span><span style="color: green;">""</span>,<span style="color: green;">"frequencyCode":</span><span style="color: green;">""</span>,<span style="color: green;">"frequencyName":</span><span style="color: green;">""</span>,<span style="color: green;">"hospitalCode":</span><span style="color: green;">""</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"noBackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"packageCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"payChannel":</span><span style="color: green;">""</span>,<span style="color: green;">"payChannelName":</span><span style="color: green;">""</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"payFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeq":</span><span style="color: green;">""</span>,<span style="color: green;">"regDate":</span><span style="color: green;">""</span>,<span style="color: green;">"seeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sendTerminalName":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"serialNum":</span><span style="color: green;">0</span>,<span style="color: green;">"singlePrice":</span><span style="color: green;">0</span>,<span style="color: green;">"spesc":</span><span style="color: green;">""</span>,<span style="color: green;">"state":</span><span style="color: green;">""</span>,<span style="color: green;">"transType":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>,<span style="color: green;">"usageCode":</span><span style="color: green;">""</span>,<span style="color: green;">"usageName":</span><span style="color: green;">""</span>}],<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"regDate":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="212u3010rf212s126u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u5217u88680a3ca20id3d212u3010rf212s126u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u5217u88683e203ca3e">2.1.2【RF2.1.2S126】查询门诊患者费用支付状态列表
<a id=2.1.2【RF2.1.2S126】查询门诊患者费用支付状态列表> </a></h2>
<p></p>
<h3 id="-4">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/recipe/pay-state/query</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询门诊处方支付状态列表</p>
<h3 id="-5">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeList</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号List</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"clinicCode":"","recipeList":[{"recipeNo":"","sequenceNo":0}]}</pre>
<h3 id="-6">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费标记</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-2">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"payFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="213u3010rf213s127u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u4e0eu660eu7ec60a3ca20id3d213u3010rf213s127u3011u67e5u8be2u95e8u8bcau60a3u8005u8d39u7528u652fu4ed8u72b6u6001u4e0eu660eu7ec63e203ca3e">2.1.3【RF2.1.3S127】查询门诊患者费用支付状态与明细
<a id=2.1.3【RF2.1.3S127】查询门诊患者费用支付状态与明细> </a></h2>
<p></p>
<h3 id="-7">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/internet/order/Prescription/query</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询处方支付状态与明细</p>
<h3 id="-8">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> endDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结束日期yyyy-MM-dd</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> patientId</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> startDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开始日期yyyy-MM-dd</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> status</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费状态</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"endDate":"","patientId":"","startDate":"","status":""}</pre>
<h3 id="-9">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> depCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> depName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctorCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctorName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生姓名</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> insuranceCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">保险公司编码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isRefundFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自定义一个标志用可退数量和确认标志判断，同时满足为1，可退</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> noBackNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者住院号</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费序列</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立时间</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号序号</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> seeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊序号</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> status</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费状态</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totalCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-3">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"depCode":</span><span style="color: green;">""</span>,<span style="color: green;">"depName":</span><span style="color: green;">""</span>,<span style="color: green;">"doctorCode":</span><span style="color: green;">""</span>,<span style="color: green;">"doctorName":</span><span style="color: green;">""</span>,<span style="color: green;">"insuranceCode":</span><span style="color: green;">""</span>,<span style="color: green;">"isRefundFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"noBackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeq":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeTime":</span><span style="color: green;">""</span>,<span style="color: green;">"regNo":</span><span style="color: green;">""</span>,<span style="color: green;">"seeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"status":</span><span style="color: green;">""</span>,<span style="color: green;">"totalCost":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="215u3010rf215s129u3011u67e5u8be2u4f4fu9662u60a3u8005u65e5u8d26u5355u4fe1u606f0a3ca20id3d215u3010rf215s129u3011u67e5u8be2u4f4fu9662u60a3u8005u65e5u8d26u5355u4fe1u606f3e203ca3e">2.1.5【RF2.1.5S129】查询住院患者日账单信息
<a id=2.1.5【RF2.1.5S129】查询住院患者日账单信息> </a></h2>
<p></p>
<h3 id="-10">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/daily/bill/get</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询住院患者日账单信息</p>
<h3 id="-11">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>inpatientNo</td>
<td>是</td>
<td></td>
<td>住院流水号</td>
</tr>
<tr>
<td>beginDate</td>
<td>是</td>
<td></td>
<td>查询开始时间yyyy-mm-dd</td>
</tr>
<tr>
<td>endDate</td>
<td>是</td>
<td></td>
<td>查询结束时间yyyy-mm-dd</td>
</tr>
</tbody>
</table>
<h3 id="-12">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">账单出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床号</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者当前所在科室名称</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inFeeDetailList</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">返回费用明细项目</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-2-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算序号</span></td><td key=5></td></tr><tr key=0-1-2-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> broughtFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否外出带药 0-否 1-是</span></td><td key=5></td></tr><tr key=0-1-2-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> centerItemGrade</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医保目录等级</span></td><td key=5></td></tr><tr key=0-1-2-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> cost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总价</span></td><td key=5></td></tr><tr key=0-1-2-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-1-2-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用产生日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扣费科室名称</span></td><td key=5></td></tr><tr key=0-1-2-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeOperName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费人</span></td><td key=5></td></tr><tr key=0-1-2-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeOpercode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费人编码</span></td><td key=5></td></tr><tr key=0-1-2-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeTypeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用类别编码</span></td><td key=5></td></tr><tr key=0-1-2-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeTypeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用类型名称</span></td><td key=5></td></tr><tr key=0-1-2-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> gbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目国家编码</span></td><td key=5></td></tr><tr key=0-1-2-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-2-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用项目名称</span></td><td key=5></td></tr><tr key=0-1-2-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品非药品标识，'0'代表非药品，'1'代表药品</span></td><td key=5></td></tr><tr key=0-1-2-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicalteamCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗组编码</span></td><td key=5></td></tr><tr key=0-1-2-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicalteamName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗组名称</span></td><td key=5></td></tr><tr key=0-1-2-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> moExecSqn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行单号</span></td><td key=5></td></tr><tr key=0-1-2-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-2-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-1-2-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区</span></td><td key=5></td></tr><tr key=0-1-2-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> orderSortId</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> orderSubSortId</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱子序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-2-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5></td></tr><tr key=0-1-2-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-2-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否发药 0-否 1-是</span></td><td key=5></td></tr><tr key=0-1-2-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人编码</span></td><td key=5></td></tr><tr key=0-1-2-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendOperName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人名称</span></td><td key=5></td></tr><tr key=0-1-2-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-2-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-2-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者当前所在病区名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者病历号</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prepayCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-4">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"bedNo":</span><span style="color: green;">""</span>,<span style="color: green;">"inDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"inFeeDetailList":</span>[{<span style="color: green;">"balanceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"broughtFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"centerItemGrade":</span><span style="color: green;">""</span>,<span style="color: green;">"cost":</span><span style="color: green;">0</span>,<span style="color: green;">"ecoCost":</span><span style="color: green;">0</span>,<span style="color: green;">"feeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeOperName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeOpercode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeTypeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeTypeName":</span><span style="color: green;">""</span>,<span style="color: green;">"gbCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"itemType":</span><span style="color: green;">""</span>,<span style="color: green;">"medicalteamCode":</span><span style="color: green;">""</span>,<span style="color: green;">"medicalteamName":</span><span style="color: green;">""</span>,<span style="color: green;">"moExecSqn":</span><span style="color: green;">""</span>,<span style="color: green;">"moOrder":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"orderSortId":</span><span style="color: green;">0</span>,<span style="color: green;">"orderSubSortId":</span><span style="color: green;">0</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sendDate":</span><span style="color: green;">""</span>,<span style="color: green;">"sendFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"sendOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"sendOperName":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"specs":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>,<span style="color: green;">"unitPrice":</span><span style="color: green;">0</span>}],<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"prepayCost":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="216u3010rf216s130u3011u67e5u8be2u4f4fu9662u60a3u8005u9884u4ea4u91d1u4fe1u606f0a3ca20id3d216u3010rf216s130u3011u67e5u8be2u4f4fu9662u60a3u8005u9884u4ea4u91d1u4fe1u606f3e203ca3e">2.1.6【RF2.1.6S130】查询住院患者预交金信息
<a id=2.1.6【RF2.1.6S130】查询住院患者预交金信息> </a></h2>
<p></p>
<h3 id="-13">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/deposit/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询患者住院预交金信息</p>
<h3 id="-14">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>pageSize</td>
<td>否</td>
<td></td>
<td></td>
</tr>
<tr>
<td>pageNum</td>
<td>否</td>
<td></td>
<td></td>
</tr>
<tr>
<td>orderBy</td>
<td>否</td>
<td></td>
<td></td>
</tr>
<tr>
<td>orderType</td>
<td>否</td>
<td></td>
<td></td>
</tr>
<tr>
<td>patientNo</td>
<td>否</td>
<td></td>
<td>住院号</td>
</tr>
<tr>
<td>cardNo</td>
<td>否</td>
<td></td>
<td>门诊病历号</td>
</tr>
<tr>
<td>inpatientNo</td>
<td>是</td>
<td></td>
<td>住院流水号</td>
</tr>
</tbody>
</table>
<h3 id="-15">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">查询住院预交金信息出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> costPrepay</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">记账金额</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> leftPrepay</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">剩余金额</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> list</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金明细</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-2-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceOpercode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算人代码</span></td><td key=5></td></tr><tr key=0-1-2-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceStatus</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算标志 0:未结算；1:已结算 2:已结转</span></td><td key=5></td></tr><tr key=0-1-2-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> cancelcheckDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">取消核查时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> cancelcheckOper</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">取消核查操作员</span></td><td key=5></td></tr><tr key=0-1-2-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> changeBalanceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">转押金时结算序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> checkDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">核查时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> checkFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">核查标记</span></td><td key=5></td></tr><tr key=0-1-2-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> checkNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">审核序号</span></td><td key=5></td></tr><tr key=0-1-2-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> checkOper</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">核查操作员</span></td><td key=5></td></tr><tr key=0-1-2-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dayBalanceDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日结日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dayBalanceFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日结标志</span></td><td key=5></td></tr><tr key=0-1-2-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dayBalanceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日结号</span></td><td key=5></td></tr><tr key=0-1-2-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dayBalanceOper</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日结操作人</span></td><td key=5></td></tr><tr key=0-1-2-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室代码</span></td><td key=5></td></tr><tr key=0-1-2-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> extFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">正常收取 1 结算召回 2</span></td><td key=5></td></tr><tr key=0-1-2-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日结标志</span></td><td key=5></td></tr><tr key=0-1-2-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> happenNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-2-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算发票号</span></td><td key=5></td></tr><tr key=0-1-2-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> memo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">存交易的原始信息</span></td><td key=5></td></tr><tr key=0-1-2-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> oldReceipt</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">原票据号</span></td><td key=5></td></tr><tr key=0-1-2-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> openBank</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开户银行</span></td><td key=5></td></tr><tr key=0-1-2-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operAccount</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开户帐户</span></td><td key=5></td></tr><tr key=0-1-2-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-2-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operDeptcode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员科室</span></td><td key=5></td></tr><tr key=0-1-2-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-2-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式 CA现金CH支票CD信用卡DB借记卡AJ转押金PO汇票PS保险帐户YS院内账户</span></td><td key=5></td></tr><tr key=0-1-2-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> postNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">pos交易流水号或支票号或汇票号</span></td><td key=5></td></tr><tr key=0-1-2-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> prepayCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金额</span></td><td key=5></td></tr><tr key=0-1-2-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> prepayStatus</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金状态0:收取；1:作废;2:补打,3结算召回作废</span></td><td key=5></td></tr><tr key=0-1-2-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> printFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">打印标志</span></td><td key=5></td></tr><tr key=0-1-2-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> receiptNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金收据号码</span></td><td key=5></td></tr><tr key=0-1-2-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> reportFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">上缴标志（1是 0否</span></td><td key=5></td></tr><tr key=0-1-2-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> statDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">统计日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">转押金时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-2-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否转押金 0非转押金，1转押金，2转押金已打印</span></td><td key=5></td></tr><tr key=0-1-2-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">转押金结算员</span></td><td key=5></td></tr><tr key=0-1-2-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> workName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">工作单位</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> returnPrepay</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金返还金额</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totPrepay</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金缴纳总额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-5">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"costPrepay":</span><span style="color: green;">0</span>,<span style="color: green;">"leftPrepay":</span><span style="color: green;">0</span>,<span style="color: green;">"list":</span>[{<span style="color: green;">"balanceDate":</span><span style="color: green;">""</span>,<span style="color: green;">"balanceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"balanceOpercode":</span><span style="color: green;">""</span>,<span style="color: green;">"balanceStatus":</span><span style="color: green;">""</span>,<span style="color: green;">"cancelcheckDate":</span><span style="color: green;">""</span>,<span style="color: green;">"cancelcheckOper":</span><span style="color: green;">""</span>,<span style="color: green;">"changeBalanceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"checkDate":</span><span style="color: green;">""</span>,<span style="color: green;">"checkFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"checkNo":</span><span style="color: green;">""</span>,<span style="color: green;">"checkOper":</span><span style="color: green;">""</span>,<span style="color: green;">"dayBalanceDate":</span><span style="color: green;">""</span>,<span style="color: green;">"dayBalanceFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"dayBalanceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"dayBalanceOper":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"extFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"feeFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"happenNo":</span><span style="color: green;">0</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"memo":</span><span style="color: green;">""</span>,<span style="color: green;">"oldReceipt":</span><span style="color: green;">""</span>,<span style="color: green;">"openBank":</span><span style="color: green;">""</span>,<span style="color: green;">"operAccount":</span><span style="color: green;">""</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"operDeptcode":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"payCode":</span><span style="color: green;">""</span>,<span style="color: green;">"postNo":</span><span style="color: green;">""</span>,<span style="color: green;">"prepayCost":</span><span style="color: green;">0</span>,<span style="color: green;">"prepayStatus":</span><span style="color: green;">""</span>,<span style="color: green;">"printFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"receiptNo":</span><span style="color: green;">""</span>,<span style="color: green;">"reportFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"statDate":</span><span style="color: green;">""</span>,<span style="color: green;">"transDate":</span><span style="color: green;">""</span>,<span style="color: green;">"transFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"transOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"workName":</span><span style="color: green;">""</span>}],<span style="color: green;">"returnPrepay":</span><span style="color: green;">0</span>,<span style="color: green;">"totPrepay":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="217u3010rf217s131u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u5206u7c7bu6c47u603buff090a3ca20id3d217u3010rf217s131u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u5206u7c7bu6c47u603buff093e203ca3e">2.1.7【RF2.1.7S131】查询住院费用（按分类汇总）
<a id=2.1.7【RF2.1.7S131】查询住院费用（按分类汇总）> </a></h2>
<p></p>
<h3 id="-16">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/daily/billAmount/get</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询住院费用（按分类汇总）</p>
<h3 id="-17">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>inpatientNo</td>
<td>是</td>
<td></td>
<td>住院流水号</td>
</tr>
<tr>
<td>beginDate</td>
<td>是</td>
<td></td>
<td>查询开始时间yyyy-mm-dd</td>
</tr>
<tr>
<td>endDate</td>
<td>是</td>
<td></td>
<td>查询结束时间yyyy-mm-dd</td>
</tr>
</tbody>
</table>
<h3 id="-18">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeInfoTos</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效费用总额（未清账）</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> typeAmout</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用分类金额</span></td><td key=5></td></tr><tr key=0-1-0-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> typeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">类型代码</span></td><td key=5></td></tr><tr key=0-1-0-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> typeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用分类名称</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prepayAmout</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金额</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totalAmout</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总费用</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-6">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"feeInfoTos":</span>[{<span style="color: green;">"typeAmout":</span><span style="color: green;">""</span>,<span style="color: green;">"typeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"typeName":</span><span style="color: green;">""</span>}],<span style="color: green;">"prepayAmout":</span><span style="color: green;">""</span>,<span style="color: green;">"totalAmout":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="218u3010rf218s132u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u65e5u671fu6c47u603buff090a3ca20id3d218u3010rf218s132u3011u67e5u8be2u4f4fu9662u8d39u7528uff08u6309u65e5u671fu6c47u603buff093e203ca3e">2.1.8【RF2.1.8S132】查询住院费用（按日期汇总）
<a id=2.1.8【RF2.1.8S132】查询住院费用（按日期汇总）> </a></h2>
<p></p>
<h3 id="-19">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/daily/bill/getByDate</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询住院费用（按日期汇总）</p>
<h3 id="-20">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> beginDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">查询开始时间yyyy-mm-dd</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> endDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">查询结束时间yyyy-mm-dd</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"beginDate":"","endDate":"","inpatientNo":""}</pre>
<h3 id="-21">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-7">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="219u3010rf219s212u3011u67e5u8be2u533bu5631u72b6u6001u548cu53efu9000u8d39u6570u91cf0a3ca20id3d219u3010rf219s212u3011u67e5u8be2u533bu5631u72b6u6001u548cu53efu9000u8d39u6570u91cf3e203ca3e">2.1.9【RF2.1.9S212】查询医嘱状态和可退费数量
<a id=2.1.9【RF2.1.9S212】查询医嘱状态和可退费数量> </a></h2>
<p></p>
<h3 id="-22">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/orderStatus/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询医嘱状态及可退费数量</p>
<h3 id="-23">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"undefined":""}</pre>
<h3 id="-24">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">付数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发票号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isPackUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位级别 1是包装单位 0是最小单位</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> noBackNum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderStatus</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">门诊医嘱状态</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> packQty</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">包装数量</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内序号</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-8">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"days":</span><span style="color: green;">0</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"isPackUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"moOrder":</span><span style="color: green;">""</span>,<span style="color: green;">"noBackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"orderStatus":</span><span style="color: green;">""</span>,<span style="color: green;">"packQty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="2110u3010rf2110s210u3011u67e5u8be2u5168u9662u95e8u8bcau7f34u8d39u660e0a3ca20id3d2110u3010rf2110s210u3011u67e5u8be2u5168u9662u95e8u8bcau7f34u8d39u660e3e203ca3e">2.1.10【RF2.1.10S210】查询全院门诊缴费明
<a id=2.1.10【RF2.1.10S210】查询全院门诊缴费明> </a></h2>
<p></p>
<h3 id="-25">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/detail/queryPage</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询门诊缴费明细列表-分页-按时间段</p>
<h3 id="-26">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> beginDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费时间范围From yyyy-MM-dd HH24:MI:SS</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> endDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费时间范围To yyyy-MM-dd HH24:MI:SS</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderBy</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageFlag</span></td><td key=1><span>boolean</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">分页Flag</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费标价</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"beginDate":"","clinicCode":"","endDate":"","orderBy":"","orderType":"","pageFlag":false,"pageNum":0,"pageSize":0,"payFlag":"","recipeNo":""}</pre>
<h3 id="-27">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> endRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasNextPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasPreviousPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isFirstPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isLastPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> list</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-5-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> baseDose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">基本剂量</span></td><td key=5></td></tr><tr key=0-1-5-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> branchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院区编码</span></td><td key=5></td></tr><tr key=0-1-5-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> classCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目分类</span></td><td key=5></td></tr><tr key=0-1-5-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-1-5-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> combNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组合号</span></td><td key=5></td></tr><tr key=0-1-5-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> confirmFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行标记</span></td><td key=5></td></tr><tr key=0-1-5-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-5-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-5-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diseCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">慢病编码</span></td><td key=5></td></tr><tr key=0-1-5-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diseName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">慢病诊断</span></td><td key=5></td></tr><tr key=0-1-5-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> docCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-5-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> docName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生名称</span></td><td key=5></td></tr><tr key=0-1-5-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> doseOnce</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">每次用量</span></td><td key=5></td></tr><tr key=0-1-5-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> doseUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">每次用量单位</span></td><td key=5></td></tr><tr key=0-1-5-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品标记</span></td><td key=5></td></tr><tr key=0-1-5-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptadss</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室地址</span></td><td key=5></td></tr><tr key=0-1-5-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDpcd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-1-5-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDpnm</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室</span></td><td key=5></td></tr><tr key=0-1-5-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用编码</span></td><td key=5></td></tr><tr key=0-1-5-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用名称</span></td><td key=5></td></tr><tr key=0-1-5-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> frequencyCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">频次编码</span></td><td key=5></td></tr><tr key=0-1-5-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> frequencyName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">频次名称</span></td><td key=5></td></tr><tr key=0-1-5-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> hospitalCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医院编码</span></td><td key=5></td></tr><tr key=0-1-5-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发票号</span></td><td key=5></td></tr><tr key=0-1-5-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-5-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称</span></td><td key=5></td></tr><tr key=0-1-5-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> noBackNum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-5-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费用</span></td><td key=5></td></tr><tr key=0-1-5-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> packageCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">复合项目代码</span></td><td key=5></td></tr><tr key=0-1-5-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位编码</span></td><td key=5></td></tr><tr key=0-1-5-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-5-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payChannel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式</span></td><td key=5></td></tr><tr key=0-1-5-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payChannelName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式名称</span></td><td key=5></td></tr><tr key=0-1-5-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠用</span></td><td key=5></td></tr><tr key=0-1-5-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费标记</span></td><td key=5></td></tr><tr key=0-1-5-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报销用</span></td><td key=5></td></tr><tr key=0-1-5-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5></td></tr><tr key=0-1-5-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方开立时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-5-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费序列</span></td><td key=5></td></tr><tr key=0-1-5-41><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> regDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-42><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> seeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">看诊序号</span></td><td key=5></td></tr><tr key=0-1-5-43><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendTerminalName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药窗口名称</span></td><td key=5></td></tr><tr key=0-1-5-44><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-45><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> serialNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-46><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> singlePrice</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-5-47><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> spesc</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-5-48><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> state</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目状态</span></td><td key=5></td></tr><tr key=0-1-5-49><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型 交易类型,1正交易，2反交易[5]</span></td><td key=5></td></tr><tr key=0-1-5-50><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-5-51><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> usageCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用法编码</span></td><td key=5></td></tr><tr key=0-1-5-52><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> usageName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">用法名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateFirstPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateLastPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatePages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatepageNums</span></td><td key=1><span>integer []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>integer</span></p></td></tr><tr key=array-1343><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nextPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prePage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> size</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> startRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> total</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-9">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"endRow":</span><span style="color: green;">0</span>,<span style="color: green;">"hasNextPage":</span><span style="color: green;">false</span>,<span style="color: green;">"hasPreviousPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isFirstPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isLastPage":</span><span style="color: green;">false</span>,<span style="color: green;">"list":</span>[{<span style="color: green;">"baseDose":</span><span style="color: green;">""</span>,<span style="color: green;">"branchCode":</span><span style="color: green;">""</span>,<span style="color: green;">"classCode":</span><span style="color: green;">""</span>,<span style="color: green;">"clinicCode":</span><span style="color: green;">""</span>,<span style="color: green;">"combNo":</span><span style="color: green;">""</span>,<span style="color: green;">"confirmFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"diseCode":</span><span style="color: green;">""</span>,<span style="color: green;">"diseName":</span><span style="color: green;">""</span>,<span style="color: green;">"docCode":</span><span style="color: green;">""</span>,<span style="color: green;">"docName":</span><span style="color: green;">""</span>,<span style="color: green;">"doseOnce":</span><span style="color: green;">0</span>,<span style="color: green;">"doseUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"drugFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptadss":</span><span style="color: green;">""</span>,<span style="color: green;">"execDpcd":</span><span style="color: green;">""</span>,<span style="color: green;">"execDpnm":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeName":</span><span style="color: green;">""</span>,<span style="color: green;">"frequencyCode":</span><span style="color: green;">""</span>,<span style="color: green;">"frequencyName":</span><span style="color: green;">""</span>,<span style="color: green;">"hospitalCode":</span><span style="color: green;">""</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"noBackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"packageCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"payChannel":</span><span style="color: green;">""</span>,<span style="color: green;">"payChannelName":</span><span style="color: green;">""</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"payFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeq":</span><span style="color: green;">""</span>,<span style="color: green;">"regDate":</span><span style="color: green;">""</span>,<span style="color: green;">"seeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sendTerminalName":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"serialNum":</span><span style="color: green;">0</span>,<span style="color: green;">"singlePrice":</span><span style="color: green;">0</span>,<span style="color: green;">"spesc":</span><span style="color: green;">""</span>,<span style="color: green;">"state":</span><span style="color: green;">""</span>,<span style="color: green;">"transType":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>,<span style="color: green;">"usageCode":</span><span style="color: green;">""</span>,<span style="color: green;">"usageName":</span><span style="color: green;">""</span>}],<span style="color: green;">"navigateFirstPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigateLastPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatePages":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatepageNums":</span>[<span style="color: green;">null</span>],<span style="color: green;">"nextPage":</span><span style="color: green;">0</span>,<span style="color: green;">"pageNum":</span><span style="color: green;">0</span>,<span style="color: green;">"pageSize":</span><span style="color: green;">0</span>,<span style="color: green;">"pages":</span><span style="color: green;">0</span>,<span style="color: green;">"prePage":</span><span style="color: green;">0</span>,<span style="color: green;">"size":</span><span style="color: green;">0</span>,<span style="color: green;">"startRow":</span><span style="color: green;">0</span>,<span style="color: green;">"total":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="u3010rf2212s230u3011u67e5u8be2u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f0a3ca20id3du3010rf2212s230u3011u67e5u8be2u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f3e203ca3e">【RF2.2.12S230】查询住院患者药品费用信息
<a id=【RF2.2.12S230】查询住院患者药品费用信息> </a></h2>
<p></p>
<h3 id="-28">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/drug/query</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询住院患者药品费用信息</p>
<h3 id="-29">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室代码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> feeBeginDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费日期-开始</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> feeEndDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费日期-结束</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> hisCancelRecipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderBy</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageFlag</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否分页</span></td><td key=5></td></tr><tr key=0-12><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-13><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-14><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院号</span></td><td key=5></td></tr><tr key=0-15><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-16><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-17><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型（1正交易，2反交易）</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"deptCode":"","execDeptCode":"","feeBeginDate":"","feeEndDate":"","hisCancelRecipeNo":"","inpatientNo":"","name":"","nurseCellCode":"","orderBy":"","orderType":"","pactCode":"","pageFlag":false,"pageNum":0,"pageSize":0,"patientNo":"","recipeNo":"","sequenceNo":0,"transType":""}</pre>
<h3 id="-30">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> endRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasNextPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasPreviousPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isFirstPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isLastPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> list</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-5-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算状态</span></td><td key=5></td></tr><tr key=0-1-5-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> centerCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医保中心项目代码</span></td><td key=5></td></tr><tr key=0-1-5-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeDoc</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主治医生</span></td><td key=5></td></tr><tr key=0-1-5-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价人</span></td><td key=5></td></tr><tr key=0-1-5-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> currentUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当前单位</span></td><td key=5></td></tr><tr key=0-1-5-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">付数/天数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室代码</span></td><td key=5></td></tr><tr key=0-1-5-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室名称</span></td><td key=5></td></tr><tr key=0-1-5-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品编码</span></td><td key=5></td></tr><tr key=0-1-5-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品名称</span></td><td key=5></td></tr><tr key=0-1-5-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugQuality</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品性质</span></td><td key=5></td></tr><tr key=0-1-5-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> drugType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品类别</span></td><td key=5></td></tr><tr key=0-1-5-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-1-5-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-1-5-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室名称</span></td><td key=5></td></tr><tr key=0-1-5-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-5-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人</span></td><td key=5></td></tr><tr key=0-1-5-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeRate</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费比率</span></td><td key=5></td></tr><tr key=0-1-5-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> homeMadeFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自制标识</span></td><td key=5></td></tr><tr key=0-1-5-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-5-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算发票号</span></td><td key=5></td></tr><tr key=0-1-5-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicalteamCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗组</span></td><td key=5></td></tr><tr key=0-1-5-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicineDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">取药科室代码</span></td><td key=5></td></tr><tr key=0-1-5-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicineDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">取药科室代码</span></td><td key=5></td></tr><tr key=0-1-5-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> moExecSqn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号</span></td><td key=5></td></tr><tr key=0-1-5-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-5-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nobackNum</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-5-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-1-5-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区名称</span></td><td key=5></td></tr><tr key=0-1-5-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operationNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">手术编码</span></td><td key=5></td></tr><tr key=0-1-5-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> orderNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-1-5-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-1-5-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> packQty</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">包装数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-1-5-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-1-5-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> paykindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别</span></td><td key=5></td></tr><tr key=0-1-5-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额</span></td><td key=5></td></tr><tr key=0-1-5-41><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5></td></tr><tr key=0-1-5-42><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码</span></td><td key=5></td></tr><tr key=0-1-5-43><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDeptName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室名称</span></td><td key=5></td></tr><tr key=0-1-5-44><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDocCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医师代码</span></td><td key=5></td></tr><tr key=0-1-5-45><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDocName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医师名称</span></td><td key=5></td></tr><tr key=0-1-5-46><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-5-47><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendDrugDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-48><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendDrugFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药状态（0 划价 2摆药 1批费）</span></td><td key=5></td></tr><tr key=0-1-5-49><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendDrugOpercode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人</span></td><td key=5></td></tr><tr key=0-1-5-50><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-51><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-5-52><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额</span></td><td key=5></td></tr><tr key=0-1-5-53><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型（1正交易，2反交易）</span></td><td key=5></td></tr><tr key=0-1-5-54><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-5-55><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> updateSequenceno</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新库存的流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateFirstPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateLastPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatePages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatepageNums</span></td><td key=1><span>integer []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>integer</span></p></td></tr><tr key=array-1345><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nextPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prePage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> size</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> startRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> total</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-10">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"endRow":</span><span style="color: green;">0</span>,<span style="color: green;">"hasNextPage":</span><span style="color: green;">false</span>,<span style="color: green;">"hasPreviousPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isFirstPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isLastPage":</span><span style="color: green;">false</span>,<span style="color: green;">"list":</span>[{<span style="color: green;">"balanceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"balanceState":</span><span style="color: green;">""</span>,<span style="color: green;">"centerCode":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDoc":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"currentUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"days":</span><span style="color: green;">0</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"drugCode":</span><span style="color: green;">""</span>,<span style="color: green;">"drugName":</span><span style="color: green;">""</span>,<span style="color: green;">"drugQuality":</span><span style="color: green;">""</span>,<span style="color: green;">"drugType":</span><span style="color: green;">""</span>,<span style="color: green;">"ecoCost":</span><span style="color: green;">0</span>,<span style="color: green;">"execDate":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeRate":</span><span style="color: green;">0</span>,<span style="color: green;">"homeMadeFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"medicalteamCode":</span><span style="color: green;">""</span>,<span style="color: green;">"medicineDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"medicineDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"moExecSqn":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"nobackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"operationNo":</span><span style="color: green;">""</span>,<span style="color: green;">"orderNo":</span><span style="color: green;">""</span>,<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"packQty":</span><span style="color: green;">0</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"paykindCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sendDrugDate":</span><span style="color: green;">""</span>,<span style="color: green;">"sendDrugFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"sendDrugOpercode":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"specs":</span><span style="color: green;">""</span>,<span style="color: green;">"totCost":</span><span style="color: green;">0</span>,<span style="color: green;">"transType":</span><span style="color: green;">""</span>,<span style="color: green;">"unitPrice":</span><span style="color: green;">0</span>,<span style="color: green;">"updateSequenceno":</span><span style="color: green;">0</span>}],<span style="color: green;">"navigateFirstPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigateLastPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatePages":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatepageNums":</span>[<span style="color: green;">null</span>],<span style="color: green;">"nextPage":</span><span style="color: green;">0</span>,<span style="color: green;">"pageNum":</span><span style="color: green;">0</span>,<span style="color: green;">"pageSize":</span><span style="color: green;">0</span>,<span style="color: green;">"pages":</span><span style="color: green;">0</span>,<span style="color: green;">"prePage":</span><span style="color: green;">0</span>,<span style="color: green;">"size":</span><span style="color: green;">0</span>,<span style="color: green;">"startRow":</span><span style="color: green;">0</span>,<span style="color: green;">"total":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="u3010rf2211s229u3011u67e5u8be2u4f4fu9662u60a3u8005u975eu836fu54c1u8d39u7528u4fe1u606f0a3ca20id3du3010rf2211s229u3011u67e5u8be2u4f4fu9662u60a3u8005u975eu836fu54c1u8d39u7528u4fe1u606f3e203ca3e">【RF2.2.11S229】查询住院患者非药品费用信息
<a id=【RF2.2.11S229】查询住院患者非药品费用信息> </a></h2>
<p></p>
<h3 id="-31">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/item/query</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询住院患者非药品费用信息</p>
<h3 id="-32">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室代码</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> feeBeginDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费日期-开始</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> feeEndDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费日期-结束</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> hisCancelRecipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderBy</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageFlag</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否分页</span></td><td key=5></td></tr><tr key=0-12><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-13><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-14><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院号</span></td><td key=5></td></tr><tr key=0-15><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-16><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-17><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型（1正交易，2反交易）</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"deptCode":"","execDeptCode":"","feeBeginDate":"","feeEndDate":"","hisCancelRecipeNo":"","inpatientNo":"","name":"","nurseCellCode":"","orderBy":"","orderType":"","pactCode":"","pageFlag":false,"pageNum":0,"pageSize":0,"patientNo":"","recipeNo":"","sequenceNo":0,"transType":""}</pre>
<h3 id="-33">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> endRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasNextPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasPreviousPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isFirstPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isLastPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> list</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-5-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> babyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否婴儿用</span></td><td key=5></td></tr><tr key=0-1-5-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算状态</span></td><td key=5></td></tr><tr key=0-1-5-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> centerCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医保中心项目代码</span></td><td key=5></td></tr><tr key=0-1-5-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeDoc</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主治医生</span></td><td key=5></td></tr><tr key=0-1-5-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chargeOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价人</span></td><td key=5></td></tr><tr key=0-1-5-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> currentUnit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当前单位</span></td><td key=5></td></tr><tr key=0-1-5-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">付数/天数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室代码</span></td><td key=5></td></tr><tr key=0-1-5-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院科室名称</span></td><td key=5></td></tr><tr key=0-1-5-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-1-5-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-1-5-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室名称</span></td><td key=5></td></tr><tr key=0-1-5-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> execOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行人代码</span></td><td key=5></td></tr><tr key=0-1-5-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-5-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeOperCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人</span></td><td key=5></td></tr><tr key=0-1-5-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeOperDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费员科室</span></td><td key=5></td></tr><tr key=0-1-5-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeRate</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费比率</span></td><td key=5></td></tr><tr key=0-1-5-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-5-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算发票号</span></td><td key=5></td></tr><tr key=0-1-5-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-5-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目标识（0非药品 2物资）</span></td><td key=5></td></tr><tr key=0-1-5-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称</span></td><td key=5></td></tr><tr key=0-1-5-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> medicalteamCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗组</span></td><td key=5></td></tr><tr key=0-1-5-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> moExecSqn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号</span></td><td key=5></td></tr><tr key=0-1-5-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-5-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nobackNum</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-5-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-1-5-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区名称</span></td><td key=5></td></tr><tr key=0-1-5-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operationNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">手术编码</span></td><td key=5></td></tr><tr key=0-1-5-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> orderNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-1-5-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-1-5-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> packageCode</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组套代码</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> packageName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组套名称</span></td><td key=5></td></tr><tr key=0-1-5-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-1-5-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-1-5-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> paykindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别</span></td><td key=5></td></tr><tr key=0-1-5-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额</span></td><td key=5></td></tr><tr key=0-1-5-41><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5></td></tr><tr key=0-1-5-42><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码</span></td><td key=5></td></tr><tr key=0-1-5-43><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDeptName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室名称</span></td><td key=5></td></tr><tr key=0-1-5-44><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDocCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医师代码</span></td><td key=5></td></tr><tr key=0-1-5-45><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeDocName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医师名称</span></td><td key=5></td></tr><tr key=0-1-5-46><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-5-47><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sendFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发放状态（0 划价 2发放（执行） 1 批费）</span></td><td key=5></td></tr><tr key=0-1-5-48><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sequenceNo</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内项目流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-5-49><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> stockDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扣库科室代码</span></td><td key=5></td></tr><tr key=0-1-5-50><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额</span></td><td key=5></td></tr><tr key=0-1-5-51><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型（1正交易，2反交易）</span></td><td key=5></td></tr><tr key=0-1-5-52><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-5-53><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> updateSequenceno</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新库存的流水号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateFirstPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateLastPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatePages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatepageNums</span></td><td key=1><span>integer []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>integer</span></p></td></tr><tr key=array-1347><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nextPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prePage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> size</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> startRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> total</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-11">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"endRow":</span><span style="color: green;">0</span>,<span style="color: green;">"hasNextPage":</span><span style="color: green;">false</span>,<span style="color: green;">"hasPreviousPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isFirstPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isLastPage":</span><span style="color: green;">false</span>,<span style="color: green;">"list":</span>[{<span style="color: green;">"babyFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"balanceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"balanceState":</span><span style="color: green;">""</span>,<span style="color: green;">"centerCode":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDoc":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"currentUnit":</span><span style="color: green;">""</span>,<span style="color: green;">"days":</span><span style="color: green;">0</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"ecoCost":</span><span style="color: green;">0</span>,<span style="color: green;">"execDate":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"execOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeOperCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeOperDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeRate":</span><span style="color: green;">0</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"invoiceNo":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"itemName":</span><span style="color: green;">""</span>,<span style="color: green;">"medicalteamCode":</span><span style="color: green;">""</span>,<span style="color: green;">"moExecSqn":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"nobackNum":</span><span style="color: green;">0</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"operationNo":</span><span style="color: green;">""</span>,<span style="color: green;">"orderNo":</span><span style="color: green;">""</span>,<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"packageCode":</span><span style="color: green;">0</span>,<span style="color: green;">"packageName":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"paykindCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sendFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"sequenceNo":</span><span style="color: green;">0</span>,<span style="color: green;">"stockDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"totCost":</span><span style="color: green;">0</span>,<span style="color: green;">"transType":</span><span style="color: green;">""</span>,<span style="color: green;">"unitPrice":</span><span style="color: green;">0</span>,<span style="color: green;">"updateSequenceno":</span><span style="color: green;">0</span>}],<span style="color: green;">"navigateFirstPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigateLastPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatePages":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatepageNums":</span>[<span style="color: green;">null</span>],<span style="color: green;">"nextPage":</span><span style="color: green;">0</span>,<span style="color: green;">"pageNum":</span><span style="color: green;">0</span>,<span style="color: green;">"pageSize":</span><span style="color: green;">0</span>,<span style="color: green;">"pages":</span><span style="color: green;">0</span>,<span style="color: green;">"prePage":</span><span style="color: green;">0</span>,<span style="color: green;">"size":</span><span style="color: green;">0</span>,<span style="color: green;">"startRow":</span><span style="color: green;">0</span>,<span style="color: green;">"total":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h1 id="22u64cdu4f5cu7c7b">2.2操作类</h1>
<p></p>
<h2 id="221u3010rf221u133u3011u7f34u7eb3u4f4fu9662u9884u4ea4u91d10a3ca20id3d221u3010rf221u133u3011u7f34u7eb3u4f4fu9662u9884u4ea4u91d13e203ca3e">2.2.1【RF2.2.1U133】缴纳住院预交金
<a id=2.2.1【RF2.2.1U133】缴纳住院预交金> </a></h2>
<p></p>
<h3 id="-34">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/deposit/payment</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
缴纳住院预交金</p>
<h3 id="-35">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> bankTransNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行交易流水</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">缴费金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> intPayCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">互联网支付方式编码</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> intPayName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">互联网支付方式名称</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> patientId</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者住院号</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payChannel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付渠道(SM扫码,CA现金,WX微信,ZF支付宝,YS院内账户)</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payMode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式(26:支付宝小程序,35:微信小程序,22:支付宝条码付,24支付宝扫码付,33:微信条码付,34:微信扫码付,54:建行条码付,53:建行扫码付,4002:农行聚合扫码付,4003:农行聚合微信条码支付,4004:农行聚合支付宝条码付,CA:现金,YS:院内账户)</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payOrderNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">第三方支付流水号</span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> posRefNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">Pos系统参考号</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">供应商编码</span></td><td key=5></td></tr><tr key=0-12><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourseTradNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">供应商交易流水</span></td><td key=5></td></tr><tr key=0-13><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"bankTransNo":"","cost":0,"intPayCode":"","intPayName":"","operCode":"","operDate":"","patientId":"","payChannel":"","payMode":"","payOrderNo":"","posRefNo":"","sourceCode":"","sourseTradNo":"","transType":""}</pre>
<h3 id="-36">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-12">出参JSON</h3>
<p>false</p>
<h2 id="222u3010rf222u190u3011u4fddu5b58u95e8u8bcau8d39u7528u4fe1u606f0a3ca20id3d222u3010rf222u190u3011u4fddu5b58u95e8u8bcau8d39u7528u4fe1u606f3e203ca3e">2.2.2【RF2.2.2U190】保存门诊费用信息
<a id=2.2.2【RF2.2.2U190】保存门诊费用信息> </a></h2>
<p></p>
<h3 id="-37">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/backfee/save</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存门诊补费记录</p>
<h3 id="-38">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> backFeeItems</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">补费信息集合</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> comboNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组合号</span></td><td key=5></td></tr><tr key=0-0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">减免金额</span></td><td key=5></td></tr><tr key=0-0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品/非药品编码</span></td><td key=5></td></tr><tr key=0-0-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-0-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-0-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">个账金额</span></td><td key=5></td></tr><tr key=0-0-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">统筹金额</span></td><td key=5></td></tr><tr key=0-0-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-0-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> seqNo</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totalCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> branchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院区编码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">门诊病历号</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价人</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">划价时间</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> tjFlag</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">0 非体检费用 1 个检费用 2团检费用</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"backFeeItems":[{"comboNo":"","ecoCost":0,"execDeptCode":"","itemCode":"","orderId":"","ownCost":0,"payCost":0,"pubCost":0,"qty":0,"recipeNo":"","seqNo":0,"totalCost":0}],"branchCode":"","cardNo":"","clinicCode":"","operCode":"","operDate":"","tjFlag":""}</pre>
<h3 id="-39">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> comboNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">组合号</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">减免金额</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品/非药品编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">个账金额</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">统筹金额</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> seqNo</span></td><td key=1><span>integer</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totalCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-13">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"comboNo":</span><span style="color: green;">""</span>,<span style="color: green;">"ecoCost":</span><span style="color: green;">0</span>,<span style="color: green;">"execDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"orderId":</span><span style="color: green;">""</span>,<span style="color: green;">"ownCost":</span><span style="color: green;">0</span>,<span style="color: green;">"payCost":</span><span style="color: green;">0</span>,<span style="color: green;">"pubCost":</span><span style="color: green;">0</span>,<span style="color: green;">"qty":</span><span style="color: green;">0</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"seqNo":</span><span style="color: green;">0</span>,<span style="color: green;">"totalCost":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="223u3010rf223u191u3011u5220u9664u95e8u8bcau8d39u7528u4fe1u606f0a3ca20id3d223u3010rf223u191u3011u5220u9664u95e8u8bcau8d39u7528u4fe1u606f3e203ca3e">2.2.3【RF2.2.3U191】删除门诊费用信息
<a id=2.2.3【RF2.2.3U191】删除门诊费用信息> </a></h2>
<p></p>
<h3 id="-40">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/backfee/delete</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
作废补费记录</p>
<h3 id="-41">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">门诊病历号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> orderId</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"cardNo":"","clinicCode":"","orderId":""}</pre>
<h3 id="-42">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-14">出参JSON</h3>
<p>false</p>
<h2 id="224u3010rf224s199u3011u67e5u5168u9662u4f4fu9662u9884u4ea4u91d1u60c5u51b5u6c47u603b0a3ca20id3d224u3010rf224s199u3011u67e5u5168u9662u4f4fu9662u9884u4ea4u91d1u60c5u51b5u6c47u603b3e203ca3e">2.2.4【RF2.2.4S199】查全院住院预交金情况汇总
<a id=2.2.4【RF2.2.4S199】查全院住院预交金情况汇总> </a></h2>
<p></p>
<h3 id="-43">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/deposit/PrepayInfo</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查全院住院预交金收费</p>
<h3 id="-44">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>hospId</td>
<td>是</td>
<td></td>
<td>医院ID</td>
</tr>
<tr>
<td>startDate</td>
<td>是</td>
<td></td>
<td>时间段开始</td>
</tr>
<tr>
<td>endDate</td>
<td>是</td>
<td></td>
<td>时间段结束</td>
</tr>
<tr>
<td>type</td>
<td>是</td>
<td></td>
<td>类型</td>
</tr>
<tr>
<td>deptCode</td>
<td>是</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>nurseCellCode</td>
<td>是</td>
<td></td>
<td>病区编码</td>
</tr>
<tr>
<td>patientNo</td>
<td>是</td>
<td></td>
<td>住院号</td>
</tr>
<tr>
<td>cardNo</td>
<td>是</td>
<td></td>
<td>患者卡号</td>
</tr>
<tr>
<td>clinicCode</td>
<td>是</td>
<td></td>
<td>门诊流水号</td>
</tr>
<tr>
<td>source</td>
<td>是</td>
<td></td>
<td>费用来源</td>
</tr>
</tbody>
</table>
<h3 id="-45">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentFeeSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号金额</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日期</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生名称</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeNum</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金次数</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mon</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">月份</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂还数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂号金额</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-15">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"appointmentFeeSum":</span><span style="color: green;">0</span>,<span style="color: green;">"appointmentNum":</span><span style="color: green;">0</span>,<span style="color: green;">"days":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"doctCode":</span><span style="color: green;">""</span>,<span style="color: green;">"doctName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeNum":</span><span style="color: green;">""</span>,<span style="color: green;">"mon":</span><span style="color: green;">""</span>,<span style="color: green;">"nowNum":</span><span style="color: green;">0</span>,<span style="color: green;">"nowSum":</span><span style="color: green;">0</span>,<span style="color: green;">"totCost":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="225u3010rf225s200u3011u67e5u8be2u5168u9662u95e8u8bcau6536u8d39u60c5u51b5u6c47u603b0a3ca20id3d225u3010rf225s200u3011u67e5u8be2u5168u9662u95e8u8bcau6536u8d39u60c5u51b5u6c47u603b3e203ca3e">2.2.5【RF2.2.5S200】查询全院门诊收费情况汇总
<a id=2.2.5【RF2.2.5S200】查询全院门诊收费情况汇总> </a></h2>
<p></p>
<h3 id="-46">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/deposit/sumfee</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
门诊收费统计</p>
<h3 id="-47">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>hospId</td>
<td>是</td>
<td></td>
<td>医院ID</td>
</tr>
<tr>
<td>startDate</td>
<td>是</td>
<td></td>
<td>时间段开始</td>
</tr>
<tr>
<td>endDate</td>
<td>是</td>
<td></td>
<td>时间段结束</td>
</tr>
<tr>
<td>type</td>
<td>是</td>
<td></td>
<td>类型</td>
</tr>
<tr>
<td>deptCode</td>
<td>是</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>nurseCellCode</td>
<td>是</td>
<td></td>
<td>病区编码</td>
</tr>
<tr>
<td>patientNo</td>
<td>是</td>
<td></td>
<td>住院号</td>
</tr>
<tr>
<td>cardNo</td>
<td>是</td>
<td></td>
<td>患者卡号</td>
</tr>
<tr>
<td>clinicCode</td>
<td>是</td>
<td></td>
<td>门诊流水号</td>
</tr>
<tr>
<td>source</td>
<td>是</td>
<td></td>
<td>费用来源</td>
</tr>
</tbody>
</table>
<h3 id="-48">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentFeeSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号金额</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日期</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生名称</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeNum</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金次数</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mon</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">月份</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂还数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂号金额</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-16">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"appointmentFeeSum":</span><span style="color: green;">0</span>,<span style="color: green;">"appointmentNum":</span><span style="color: green;">0</span>,<span style="color: green;">"days":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"doctCode":</span><span style="color: green;">""</span>,<span style="color: green;">"doctName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeNum":</span><span style="color: green;">""</span>,<span style="color: green;">"mon":</span><span style="color: green;">""</span>,<span style="color: green;">"nowNum":</span><span style="color: green;">0</span>,<span style="color: green;">"nowSum":</span><span style="color: green;">0</span>,<span style="color: green;">"totCost":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="226u3010rf226s201u3011u67e5u8be2u5168u9662u6302u53f7u8bb0u5f550a3ca20id3d226u3010rf226s201u3011u67e5u8be2u5168u9662u6302u53f7u8bb0u5f553e203ca3e">2.2.6【RF2.2.6S201】查询全院挂号记录
<a id=2.2.6【RF2.2.6S201】查询全院挂号记录> </a></h2>
<p></p>
<h3 id="-49">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/deposit/sumRegfee</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
全院挂号记录</p>
<h3 id="-50">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>hospId</td>
<td>是</td>
<td></td>
<td>医院ID</td>
</tr>
<tr>
<td>startDate</td>
<td>是</td>
<td></td>
<td>时间段开始</td>
</tr>
<tr>
<td>endDate</td>
<td>是</td>
<td></td>
<td>时间段结束</td>
</tr>
<tr>
<td>type</td>
<td>是</td>
<td></td>
<td>类型</td>
</tr>
<tr>
<td>deptCode</td>
<td>是</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>nurseCellCode</td>
<td>是</td>
<td></td>
<td>病区编码</td>
</tr>
<tr>
<td>patientNo</td>
<td>是</td>
<td></td>
<td>住院号</td>
</tr>
<tr>
<td>cardNo</td>
<td>是</td>
<td></td>
<td>患者卡号</td>
</tr>
<tr>
<td>clinicCode</td>
<td>是</td>
<td></td>
<td>门诊流水号</td>
</tr>
<tr>
<td>source</td>
<td>是</td>
<td></td>
<td>费用来源</td>
</tr>
</tbody>
</table>
<h3 id="-51">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentFeeSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号金额</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> appointmentNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预约挂号数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> days</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">日期</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生编码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doctName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生名称</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeNum</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金次数</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mon</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">月份</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂还数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nowSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">当日挂号金额</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-17">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"appointmentFeeSum":</span><span style="color: green;">0</span>,<span style="color: green;">"appointmentNum":</span><span style="color: green;">0</span>,<span style="color: green;">"days":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"doctCode":</span><span style="color: green;">""</span>,<span style="color: green;">"doctName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeNum":</span><span style="color: green;">""</span>,<span style="color: green;">"mon":</span><span style="color: green;">""</span>,<span style="color: green;">"nowNum":</span><span style="color: green;">0</span>,<span style="color: green;">"nowSum":</span><span style="color: green;">0</span>,<span style="color: green;">"totCost":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="227u3010rf227i205u3011u95e8u8bcau9000u8d39u7533u8bf70a3ca20id3d227u3010rf227i205u3011u95e8u8bcau9000u8d39u7533u8bf73e203ca3e">2.2.7【RF2.2.7I205】门诊退费申请
<a id=2.2.7【RF2.2.7I205】门诊退费申请> </a></h2>
<p></p>
<h3 id="-52">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/refundFee/apply/save</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
门诊退费申请，同等于医生站医生操作退费申请</p>
<h3 id="-53">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">挂号流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> days</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">付数/天数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> drugFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否药品</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> invoiceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发票号</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员工号</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> patientDeptId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者所在科室</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> quitApplyNum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">退费申请数量</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> returnReason</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">退费原因</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sequenceNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内流水号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"clinicCode":"","days":0,"drugFlag":"","invoiceNo":"","moOrder":"","operCode":"","operDate":"","patientDeptId":"","quitApplyNum":0,"recipeNo":"","returnReason":"","sequenceNo":""}</pre>
<h3 id="-54">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-18">出参JSON</h3>
<p>false</p>
<h2 id="228u3010rf228i214u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u6279u8d39u63a5u53e3290a3ca20id3d228u3010rf228i214u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u6279u8d39u63a5u53e3293e203ca3e">2.2.8【RF2.2.8I214】保存住院患者费用信息(住院辅材批费接口)
<a id=2.2.8【RF2.2.8I214】保存住院患者费用信息(住院辅材批费接口)> </a></h2>
<p></p>
<h3 id="-55">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/item/fee</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
住院辅材批费接口</p>
<h3 id="-56">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> feeItems</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用DTO</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> backNum</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量 string 是 </span></td><td key=5></td></tr><tr key=0-0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额 string 是</span></td><td key=5></td></tr><tr key=0-0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码 string  </span></td><td key=5></td></tr><tr key=0-0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费日期 string 是</span></td><td key=5></td></tr><tr key=0-0-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目代码 string 是</span></td><td key=5></td></tr><tr key=0-0-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemFlag</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">0非药品 2物资 string 是</span></td><td key=5></td></tr><tr key=0-0-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称 string 是</span></td><td key=5></td></tr><tr key=0-0-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moExecSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号 string  </span></td><td key=5></td></tr><tr key=0-0-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号 string  </span></td><td key=5></td></tr><tr key=0-0-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人代码 string 是</span></td><td key=5></td></tr><tr key=0-0-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码 string  </span></td><td key=5></td></tr><tr key=0-0-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额 string 是</span></td><td key=5></td></tr><tr key=0-0-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额 string 是</span></td><td key=5></td></tr><tr key=0-0-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额 string 是</span></td><td key=5></td></tr><tr key=0-0-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量 string 是</span></td><td key=5></td></tr><tr key=0-0-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendmatSequence</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出库单序列号 Integer  </span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> stockDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扣库科室代码 string  </span></td><td key=5></td></tr><tr key=0-0-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> storeSeq</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新库存的流水号(物资) Integer  </span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额 string 是</span></td><td key=5></td></tr><tr key=0-0-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位 string 是</span></td><td key=5></td></tr><tr key=0-0-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价 string 是</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payKindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别 </span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"feeItems":[{"backNum":"","ecoCost":"","execDeptCode":"","feeDate":"","itemCode":"","itemFlag":"","itemName":"","moExecSeq":"","moOrder":"","operCode":"","operDeptCode":"","ownCost":"","payCost":"","pubCost":"","qty":"","sendmatSequence":0,"stockDeptCode":"","storeSeq":0,"totCost":"","unit":"","unitPrice":""}],"inpatientNo":"","payCode":"","payKindCode":""}</pre>
<h3 id="-57">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ipFeeDetailTos</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> detailItemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">明细项目编码</span></td><td key=5></td></tr><tr key=0-1-0-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> detailItemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">明细项目名称</span></td><td key=5></td></tr><tr key=0-1-0-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-0-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeSeqNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-19">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"ipFeeDetailTos":</span>[{<span style="color: green;">"detailItemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"detailItemName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeqNo":</span><span style="color: green;">0</span>}],<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"orderId":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="229u3010rf229i217u3011u95e8u8bcau6536u8d390a3ca20id3d229u3010rf229i217u3011u95e8u8bcau6536u8d393e203ca3e">2.2.9【RF2.2.9I217】门诊收费
<a id=2.2.9【RF2.2.9I217】门诊收费> </a></h2>
<p></p>
<h3 id="-58">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/fee/save</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存门诊收费记录</p>
<h3 id="-59">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者唯一索引</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者门诊流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员ID</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> ownCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pactCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同类型</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payModeList</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-6-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行交易时间</span></td><td key=5></td></tr><tr key=0-6-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankMachine</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机器编号</span></td><td key=5></td></tr><tr key=0-6-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行名称</span></td><td key=5></td></tr><tr key=0-6-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行卡号</span></td><td key=5></td></tr><tr key=0-6-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankTransNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易流水号</span></td><td key=5></td></tr><tr key=0-6-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付金额</span></td><td key=5></td></tr><tr key=0-6-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式编码</span></td><td key=5></td></tr><tr key=0-6-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式名称</span></td><td key=5></td></tr><tr key=0-6-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pos</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">POS号</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pubCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报销金额</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeList</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费处方金额</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-8-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fee</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方金额</span></td><td key=5></td></tr><tr key=0-8-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">调用方身份编码</span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">调用方名称</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> totCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-12><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易流水号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"cardNo":"","clinicCode":"","operCode":"","ownCost":0,"pactCode":"","payCost":0,"payModeList":[{"bankDate":"","bankMachine":"","bankName":"","bankNo":"","bankTransNo":"","cost":0,"payCode":"","payName":"","pos":""}],"pubCost":0,"recipeList":[{"fee":0,"recipeNo":""}],"sourceCode":"","sourceName":"","totCost":0,"transNo":""}</pre>
<h3 id="-60">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-20">出参JSON</h3>
<p>false</p>
<h2 id="2210u3010rf2210i222u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4fddu5b58u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f290a3ca20id3d2210u3010rf2210i222u3011u4fddu5b58u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4fddu5b58u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f293e203ca3e">2.2.10【RF2.2.10I222】保存住院患者费用信息(保存住院患者药品费用信息)
<a id=2.2.10【RF2.2.10I222】保存住院患者费用信息(保存住院患者药品费用信息)> </a></h2>
<p></p>
<h3 id="-61">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/drug/fee</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存住院患者药品费用信息</p>
<h3 id="-62">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> medicineFeeItems</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用DTO</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> babyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否婴儿用药</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> backNum</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品编码</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品名称</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行医生代码</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行时间</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费时间</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moExecSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人代码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量 (最小单位的数量)</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeDocCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医生代码</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立时间</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> requestNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">申请单号</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药科室代码</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人编码</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendmatSequence</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出库单序列号</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spec</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品规格</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payKindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别 </span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"inpatientNo":"","medicineFeeItems":[{"babyFlag":"","backNum":"","drugCode":"","drugName":"","ecoCost":"","execDeptCode":"","execDocCode":"","execTime":"","feeTime":"","moExecSeq":"","moOrder":"","operCode":"","operDeptCode":"","ownCost":"","payCost":"","pubCost":"","qty":"","recipeDocCode":"","recipeTime":"","requestNo":"","sendDeptCode":"","sendDocCode":"","sendTime":"","sendmatSequence":"","spec":"","totCost":"","unit":"","unitPrice":""}],"payCode":"","payKindCode":"","transType":""}</pre>
<h3 id="-63">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeSeqNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-21">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"drugCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"orderId":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeqNo":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="2211u3010rf2211u057u3011u9662u5185u5361u5145u503c0a3ca20id3d2211u3010rf2211u057u3011u9662u5185u5361u5145u503c3e203ca3e">2.2.11【RF2.2.11U057】院内卡充值
<a id=2.2.11【RF2.2.11U057】院内卡充值> </a></h2>
<p></p>
<h3 id="-64">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/deposit/payment</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
院内卡充值</p>
<h3 id="-65">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> accountNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">账户号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者档案号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> markNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">实体卡卡号</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员编码</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDate</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payMode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式(26:支付宝小程序,35:微信小程序,22:支付宝条码付,24支付宝扫码付,33:微信条码付,34:微信扫码付,54:建行条码付,53:建行扫码付,4002:农行聚合扫码付,4003:农行聚合微信条码支付,4004:农行聚合支付宝条码付,CA:现金,YS:院内账户)</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payOrderNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">第三方支付流水号</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> postransNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">三方交易流水号</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> prepayCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">充值金额</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> prepayType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式(SM扫码,CA现金,WX微信,ZF支付宝,YS院内账户)</span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">供应商编码</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourseTradNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">供应商交易流水</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"accountNo":"","cardNo":"","markNo":"","operCode":"","operDate":"","payMode":"","payOrderNo":"","postransNo":"","prepayCost":0,"prepayType":"","sourceCode":"","sourseTradNo":""}</pre>
<h3 id="-66">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-22">出参JSON</h3>
<p>false</p>
<h2 id="2212u3010rf2212d232u3011u5220u9664u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u53d6u6d88u6279u8d39u63a5u53e3290a3ca20id3d2212u3010rf2212d232u3011u5220u9664u4f4fu9662u60a3u8005u8d39u7528u4fe1u606f28u4f4fu9662u8f85u6750u53d6u6d88u6279u8d39u63a5u53e3293e203ca3e">2.2.12【RF2.2.12D232】删除住院患者费用信息(住院辅材取消批费接口)
<a id=2.2.12【RF2.2.12D232】删除住院患者费用信息(住院辅材取消批费接口)> </a></h2>
<p></p>
<h3 id="-67">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/item/fee/cancel</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
住院辅材取消批费接口</p>
<h3 id="-68">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员编码 </span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作科室编码</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号 </span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeSeqNoList</span></td><td key=1><span>string []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方流水号</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>string</span></p></td></tr><tr key=array-1349><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"inpatientNo":"","operCode":"","operDeptCode":"","recipeNo":"","recipeSeqNoList":[null]}</pre>
<h3 id="-69">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-23">出参JSON</h3>
<p>false</p>
<h2 id="2213u3010rf2213d236u3011u5220u9664u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f0a3ca20id3d2213u3010rf2213d236u3011u5220u9664u4f4fu9662u60a3u8005u836fu54c1u8d39u7528u4fe1u606f3e203ca3e">2.2.13【RF2.2.13D236】删除住院患者药品费用信息
<a id=2.2.13【RF2.2.13D236】删除住院患者药品费用信息> </a></h2>
<p></p>
<h3 id="-70">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/drug/fee/cancel</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
删除住院患者药品费用信息</p>
<h3 id="-71">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员编码 </span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作科室编码</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号 </span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeSeqNoList</span></td><td key=1><span>string []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内流水号</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>string</span></p></td></tr><tr key=array-1351><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"inpatientNo":"","operCode":"","operDeptCode":"","recipeNo":"","recipeSeqNoList":[null]}</pre>
<h3 id="-72">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-24">出参JSON</h3>
<p>false</p>
<h2 id="2214u3010rf2214i237u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u975eu836fuff090a3ca20id3d2214u3010rf2214i237u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u975eu836fuff093e203ca3e">2.2.14【RF2.2.14I237】住院患者费用信息保存接口（非药）
<a id=2.2.14【RF2.2.14I237】住院患者费用信息保存接口（非药）> </a></h2>
<p></p>
<h3 id="-73">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/inhos/nondrug</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存住院患者费用信息（非药）</p>
<h3 id="-74">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payKindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别 </span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> unionFeeItems</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用DTO</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-4-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> babyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否婴儿用药</span></td><td key=5></td></tr><tr key=0-4-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> backNum</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-4-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-4-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-4-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行医生代码</span></td><td key=5></td></tr><tr key=0-4-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行时间</span></td><td key=5></td></tr><tr key=0-4-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费时间</span></td><td key=5></td></tr><tr key=0-4-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeoperDeptcode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费员执行科室</span></td><td key=5></td></tr><tr key=0-4-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目代码 string 是</span></td><td key=5></td></tr><tr key=0-4-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemFeeType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费方式</span></td><td key=5></td></tr><tr key=0-4-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">0非药品 2物资 string 是</span></td><td key=5></td></tr><tr key=0-4-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称 string 是</span></td><td key=5></td></tr><tr key=0-4-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目类别</span></td><td key=5></td></tr><tr key=0-4-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moExecSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号</span></td><td key=5></td></tr><tr key=0-4-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-4-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人代码</span></td><td key=5></td></tr><tr key=0-4-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码</span></td><td key=5></td></tr><tr key=0-4-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-4-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownFlag</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费处方标识 0-非自费处方 1-自费处方</span></td><td key=5></td></tr><tr key=0-4-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-4-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额</span></td><td key=5></td></tr><tr key=0-4-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量 (最小单位的数量)</span></td><td key=5></td></tr><tr key=0-4-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeDocCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医生代码</span></td><td key=5></td></tr><tr key=0-4-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立时间</span></td><td key=5></td></tr><tr key=0-4-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> requestNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">申请单号</span></td><td key=5></td></tr><tr key=0-4-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药科室代码</span></td><td key=5></td></tr><tr key=0-4-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人编码</span></td><td key=5></td></tr><tr key=0-4-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-4-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendmatSequence</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出库单序列号</span></td><td key=5></td></tr><tr key=0-4-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spec</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品规格</span></td><td key=5></td></tr><tr key=0-4-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> stockDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扣库科室代码 string  </span></td><td key=5></td></tr><tr key=0-4-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> storeSeq</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新库存的流水号(物资) Integer  </span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-4-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额</span></td><td key=5></td></tr><tr key=0-4-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-4-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"inpatientNo":"","payCode":"","payKindCode":"","transType":"","unionFeeItems":[{"babyFlag":"","backNum":"","ecoCost":"","execDeptCode":"","execDocCode":"","execTime":"","feeTime":"","feeoperDeptcode":"","itemCode":"","itemFeeType":"","itemFlag":"","itemName":"","itemType":"","moExecSeq":"","moOrder":"","operCode":"","operDeptCode":"","ownCost":"","ownFlag":"","payCost":"","pubCost":"","qty":"","recipeDocCode":"","recipeTime":"","requestNo":"","sendDeptCode":"","sendDocCode":"","sendTime":"","sendmatSequence":"","spec":"","stockDeptCode":"","storeSeq":0,"totCost":"","unit":"","unitPrice":""}]}</pre>
<h3 id="-75">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ipFeeDetailTos</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> detailItemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">明细项目编码</span></td><td key=5></td></tr><tr key=0-1-0-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> detailItemName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">明细项目名称</span></td><td key=5></td></tr><tr key=0-1-0-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-0-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> recipeSeqNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方内序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-25">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"ipFeeDetailTos":</span>[{<span style="color: green;">"detailItemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"detailItemName":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeqNo":</span><span style="color: green;">0</span>}],<span style="color: green;">"itemCode":</span><span style="color: green;">""</span>,<span style="color: green;">"orderId":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="2215u3010rf2215i238u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u836fu54c1uff090a3ca20id3d2215u3010rf2215i238u3011u4f4fu9662u60a3u8005u8d39u7528u4fe1u606fu4fddu5b58u63a5u53e3uff08u836fu54c1uff093e203ca3e">2.2.15【RF2.2.15I238】住院患者费用信息保存接口（药品）
<a id=2.2.15【RF2.2.15I238】住院患者费用信息保存接口（药品）> </a></h2>
<p></p>
<h3 id="-76">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/ip/inhos/drug</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存住院患者费用信息（药品）</p>
<h3 id="-77">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payKindCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别 </span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易类型</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> unionFeeItems</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用DTO</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-4-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> babyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否婴儿用药</span></td><td key=5></td></tr><tr key=0-4-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> backNum</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">可退数量</span></td><td key=5></td></tr><tr key=0-4-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ecoCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">优惠金额</span></td><td key=5></td></tr><tr key=0-4-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室代码</span></td><td key=5></td></tr><tr key=0-4-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行医生代码</span></td><td key=5></td></tr><tr key=0-4-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行时间</span></td><td key=5></td></tr><tr key=0-4-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费时间</span></td><td key=5></td></tr><tr key=0-4-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeoperDeptcode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费员执行科室</span></td><td key=5></td></tr><tr key=0-4-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目代码 string 是</span></td><td key=5></td></tr><tr key=0-4-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemFeeType</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费方式</span></td><td key=5></td></tr><tr key=0-4-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">0非药品 2物资 string 是</span></td><td key=5></td></tr><tr key=0-4-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目名称 string 是</span></td><td key=5></td></tr><tr key=0-4-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> itemType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目类别</span></td><td key=5></td></tr><tr key=0-4-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moExecSeq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱执行单流水号</span></td><td key=5></td></tr><tr key=0-4-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> moOrder</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱流水号</span></td><td key=5></td></tr><tr key=0-4-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">计费人代码</span></td><td key=5></td></tr><tr key=0-4-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDeptCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立科室代码</span></td><td key=5></td></tr><tr key=0-4-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-4-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ownFlag</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费处方标识 0-非自费处方 1-自费处方</span></td><td key=5></td></tr><tr key=0-4-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-4-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pubCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">公费金额</span></td><td key=5></td></tr><tr key=0-4-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> qty</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数量 (最小单位的数量)</span></td><td key=5></td></tr><tr key=0-4-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeDocCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立医生代码</span></td><td key=5></td></tr><tr key=0-4-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeTime</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">开立时间</span></td><td key=5></td></tr><tr key=0-4-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> requestNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">申请单号</span></td><td key=5></td></tr><tr key=0-4-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药科室代码</span></td><td key=5></td></tr><tr key=0-4-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药人编码</span></td><td key=5></td></tr><tr key=0-4-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-4-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sendmatSequence</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出库单序列号</span></td><td key=5></td></tr><tr key=0-4-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spec</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品规格</span></td><td key=5></td></tr><tr key=0-4-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> stockDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扣库科室代码 string  </span></td><td key=5></td></tr><tr key=0-4-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> storeSeq</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新库存的流水号(物资) Integer  </span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-4-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额</span></td><td key=5></td></tr><tr key=0-4-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-4-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unitPrice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"inpatientNo":"","payCode":"","payKindCode":"","transType":"","unionFeeItems":[{"babyFlag":"","backNum":"","ecoCost":"","execDeptCode":"","execDocCode":"","execTime":"","feeTime":"","feeoperDeptcode":"","itemCode":"","itemFeeType":"","itemFlag":"","itemName":"","itemType":"","moExecSeq":"","moOrder":"","operCode":"","operDeptCode":"","ownCost":"","ownFlag":"","payCost":"","pubCost":"","qty":"","recipeDocCode":"","recipeTime":"","requestNo":"","sendDeptCode":"","sendDocCode":"","sendTime":"","sendmatSequence":"","spec":"","stockDeptCode":"","storeSeq":0,"totCost":"","unit":"","unitPrice":""}]}</pre>
<h3 id="-78">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">项目编码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最小费用代码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> orderId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医嘱号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeSeqNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-26">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"drugCode":</span><span style="color: green;">""</span>,<span style="color: green;">"feeCode":</span><span style="color: green;">""</span>,<span style="color: green;">"orderId":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeNo":</span><span style="color: green;">""</span>,<span style="color: green;">"recipeSeqNo":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="2216u3010rf2216i239u3011u95e8u8bcau6536u8d39u4fe1u606fu7ed3u7b970a3ca20id3d2216u3010rf2216i239u3011u95e8u8bcau6536u8d39u4fe1u606fu7ed3u7b973e203ca3e">2.2.16【RF2.2.16I239】门诊收费信息结算
<a id=2.2.16【RF2.2.16I239】门诊收费信息结算> </a></h2>
<p></p>
<h3 id="-79">基本信息</h3>
<p><strong>Path：</strong> /api/v1/fee/op/fee/balanceClinicFee</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存门诊费用信息</p>
<h3 id="-80">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> cardNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者唯一索引</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> clinicCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者门诊流水号</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> operCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员ID</span></td><td key=5></td></tr><tr key=0-3><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> ownCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自费金额</span></td><td key=5></td></tr><tr key=0-4><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pactCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同类型</span></td><td key=5></td></tr><tr key=0-5><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自付金额</span></td><td key=5></td></tr><tr key=0-6><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> payModeList</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-6-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行交易时间</span></td><td key=5></td></tr><tr key=0-6-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankMachine</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">机器编号</span></td><td key=5></td></tr><tr key=0-6-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行名称</span></td><td key=5></td></tr><tr key=0-6-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">银行卡号</span></td><td key=5></td></tr><tr key=0-6-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bankTransNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易流水号</span></td><td key=5></td></tr><tr key=0-6-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付金额</span></td><td key=5></td></tr><tr key=0-6-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式编码</span></td><td key=5></td></tr><tr key=0-6-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> payName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">支付方式名称</span></td><td key=5></td></tr><tr key=0-6-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pos</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">POS号</span></td><td key=5></td></tr><tr key=0-7><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> pubCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">报销金额</span></td><td key=5></td></tr><tr key=0-8><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> recipeList</span></td><td key=1><span>object []</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">收费处方金额</span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-8-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fee</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方金额</span></td><td key=5></td></tr><tr key=0-8-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> recipeNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">处方号</span></td><td key=5></td></tr><tr key=0-9><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceCode</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">调用方身份编码</span></td><td key=5></td></tr><tr key=0-10><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> sourceName</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">调用方名称</span></td><td key=5></td></tr><tr key=0-11><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> totCost</span></td><td key=1><span>number</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总金额</span></td><td key=5></td></tr><tr key=0-12><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> transNo</span></td><td key=1><span>string</span></td><td key=2>必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">交易流水号</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"cardNo":"","clinicCode":"","operCode":"","ownCost":0,"pactCode":"","payCost":0,"payModeList":[{"bankDate":"","bankMachine":"","bankName":"","bankNo":"","bankTransNo":"","cost":0,"payCode":"","payName":"","pos":""}],"pubCost":0,"recipeList":[{"fee":0,"recipeNo":""}],"sourceCode":"","sourceName":"","totCost":0,"transNo":""}</pre>
<h3 id="-81">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-27">出参JSON</h3>
<p>false</p>

            <footer class="m-footer">
              <p>Build by <a href="https://ymfe.org/">YMFE</a>.</p>
            </footer>
          </div>
        </div>
      </body>
      </html>
      