<!DOCTYPE html>
      <html>
      <head>
      <title>curr-web</title>
      <meta charset="utf-8" />
      <style>@charset "UTF-8";
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.2);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
  font-size: 13px;
  line-height: 25px;
  color: #393838;
  position: relative;
}

table {
  margin: 10px 0 15px 0;
  border-collapse: collapse;
}

td,
th {
  border: 1px solid #ddd;
  padding: 3px 10px;
}

th {
  padding: 5px 10px;
}

a, a:link, a:visited {
  color: #34495e;
  text-decoration: none;
}

a:hover, a:focus {
  color: #59d69d;
  text-decoration: none;
}

a img {
  border: none;
}

p {
  padding-left: 10px;
  margin-bottom: 9px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #404040;
  line-height: 36px;
}

h1 {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
  line-height: 50px;
}

h2 {
  font-size: 28px;
  padding-top: 10px;
  padding-bottom: 10px;
}

h3 {
  clear: both;
  font-weight: 400;
  margin-top: 20px;
  margin-bottom: 20px;
  border-left: 3px solid #59d69d;
  padding-left: 8px;
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 13px;
}

hr {
  margin: 0 0 19px;
  border: 0;
  border-bottom: 1px solid #ccc;
}

blockquote {
  padding: 13px 13px 21px 15px;
  margin-bottom: 18px;
  font-family: georgia, serif;
  font-style: italic;
}

blockquote:before {
  font-size: 40px;
  margin-left: -10px;
  font-family: georgia, serif;
  color: #eee;
}

blockquote p {
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  margin-bottom: 0;
  font-style: italic;
}

code,
pre {
  font-family: Monaco, Andale Mono, Courier New, monospace;
}

code {
  background-color: #fee9cc;
  color: rgba(0, 0, 0, 0.75);
  padding: 1px 3px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

pre {
  display: block;
  padding: 14px;
  margin: 0 0 18px;
  line-height: 16px;
  font-size: 11px;
  border: 1px solid #d9d9d9;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f6f6f6;
}

pre code {
  background-color: #f6f6f6;
  color: #737373;
  font-size: 11px;
  padding: 0;
}

sup {
  font-size: 0.83em;
  vertical-align: super;
  line-height: 0;
}

* {
  -webkit-print-color-adjust: exact;
}

@media print {
  body,
  code,
  pre code,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black;
  }

  table,
  pre {
    page-break-inside: avoid;
  }
}
html,
body {
  height: 100%;
}

.table-of-contents {
  position: fixed;
  top: 61px;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  width: 260px;
}

.table-of-contents > ul > li > a {
  font-size: 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.table-of-contents ul {
  overflow: auto;
  margin: 0px;
  height: 100%;
  padding: 0px 0px;
  box-sizing: border-box;
  list-style-type: none;
}

.table-of-contents ul li {
  padding-left: 20px;
}

.table-of-contents a {
  padding: 2px 0px;
  display: block;
  text-decoration: none;
}

.content-right {
  max-width: 700px;
  margin-left: 290px;
  padding-left: 70px;
  flex-grow: 1;
}
.content-right h2:target {
  padding-top: 80px;
}

body > p {
  margin-left: 30px;
}

body > table {
  margin-left: 30px;
}

body > pre {
  margin-left: 30px;
}

.curProject {
  position: fixed;
  top: 20px;
  font-size: 25px;
  color: black;
  margin-left: -240px;
  width: 240px;
  padding: 5px;
  line-height: 25px;
  box-sizing: border-box;
}

.g-doc {
  margin-top: 56px;
  padding-top: 24px;
  display: flex;
}

.curproject-name {
  font-size: 42px;
}

.m-header {
  background: #32363a;
  height: 56px;
  line-height: 56px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
}
.m-header .title {
  font-size: 22px;
  color: #fff;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  margin-left: 16px;
  padding: 0;
  line-height: 56px;
  border: none;
}
.m-header .nav {
  color: #fff;
  font-size: 16px;
  position: absolute;
  right: 32px;
  top: 0;
}
.m-header .nav a {
  color: #fff;
  margin-left: 16px;
  padding: 8px;
  transition: color .2s;
}
.m-header .nav a:hover {
  color: #59d69d;
}

.m-footer {
  border-top: 1px solid #ddd;
  padding-top: 16px;
  padding-bottom: 16px;
}

/*# sourceMappingURL=defaultTheme.css.map */
</style>
      </head>
      <body>
        <div class="m-header">
          <a href="#" style="display: inherit;"><svg class="svg" width="32px" height="32px" viewBox="0 0 64 64" version="1.1"><title>Icon</title><desc>Created with Sketch.</desc><defs><linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1"><stop stop-color="#FFFFFF" offset="0%"></stop><stop stop-color="#F2F2F2" offset="100%"></stop></linearGradient><circle id="path-2" cx="31.9988602" cy="31.9988602" r="2.92886048"></circle><filter x="-85.4%" y="-68.3%" width="270.7%" height="270.7%" filterUnits="objectBoundingBox" id="filter-3"><feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset><feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur><feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.159703351 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix></filter></defs><g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="大屏幕"><g id="Icon"><circle id="Oval-1" fill="url(#linearGradient-1)" cx="32" cy="32" r="32"></circle><path d="M36.7078009,31.8054514 L36.7078009,51.7110548 C36.7078009,54.2844537 34.6258634,56.3695395 32.0579205,56.3695395 C29.4899777,56.3695395 27.4099998,54.0704461 27.4099998,51.7941246 L27.4099998,31.8061972 C27.4099998,29.528395 29.4909575,27.218453 32.0589004,27.230043 C34.6268432,27.241633 36.7078009,29.528395 36.7078009,31.8054514 Z" id="blue" fill="#2359F1" fill-rule="nonzero"></path><path d="M45.2586091,17.1026914 C45.2586091,17.1026914 45.5657231,34.0524383 45.2345291,37.01141 C44.9033351,39.9703817 43.1767091,41.6667796 40.6088126,41.6667796 C38.040916,41.6667796 35.9609757,39.3676862 35.9609757,37.0913646 L35.9609757,17.1034372 C35.9609757,14.825635 38.0418959,12.515693 40.6097924,12.527283 C43.177689,12.538873 45.2586091,14.825635 45.2586091,17.1026914 Z" id="green" fill="#57CF27" fill-rule="nonzero" transform="translate(40.674608, 27.097010) rotate(60.000000) translate(-40.674608, -27.097010) "></path><path d="M28.0410158,17.0465598 L28.0410158,36.9521632 C28.0410158,39.525562 25.9591158,41.6106479 23.3912193,41.6106479 C20.8233227,41.6106479 18.7433824,39.3115545 18.7433824,37.035233 L18.7433824,17.0473055 C18.7433824,14.7695034 20.8243026,12.4595614 23.3921991,12.4711513 C25.9600956,12.4827413 28.0410158,14.7695034 28.0410158,17.0465598 Z" id="red" fill="#FF561B" fill-rule="nonzero" transform="translate(23.392199, 27.040878) rotate(-60.000000) translate(-23.392199, -27.040878) "></path><g id="inner-round"><use fill="black" fill-opacity="1" filter="url(#filter-3)" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use><use fill="#F7F7F7" fill-rule="evenodd" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-2"></use></g></g></g></g></svg></a>
          <a href="#"><h1 class="title">YAPI 接口文档</h1></a>
          <div class="nav">
            <a href="https://hellosean1025.github.io/yapi/">YApi</a>
          </div>
        </div>
        <div class="g-doc">
          <div class="table-of-contents"><ul><li><a href="#11u67e5u8be2u670du52a1">1.1查询服务</a><ul><li><a href="#111u3010rc111s001u3011u67e5u8be2u79d1u5ba4u5217u88680a3ca20id3d111u3010rc111s001u3011u67e5u8be2u79d1u5ba4u5217u88683e203ca3e">1.1.1【RC1.1.1S001】查询科室列表
<a id=1.1.1【RC1.1.1S001】查询科室列表> </a></a></li><li><a href="#112u3010rc112s002u3011u83b7u53d6u79d1u5ba4u4fe1u606f0a3ca20id3d112u3010rc112s002u3011u83b7u53d6u79d1u5ba4u4fe1u606f3e203ca3e">1.1.2【RC1.1.2S002】获取科室信息
<a id=1.1.2【RC1.1.2S002】获取科室信息> </a></a></li><li><a href="#113u3010rc113s003u3011u67e5u8be2u4ebau5458u5217u88680a3ca20id3d113u3010rc113s003u3011u67e5u8be2u4ebau5458u5217u88683e203ca3e">1.1.3【RC1.1.3S003】查询人员列表
<a id=1.1.3【RC1.1.3S003】查询人员列表> </a></a></li><li><a href="#114u3010rc114s004u3011u67e5u8be2u4ebau5458u4fe1u606f0a3ca20id3d114u3010rc114s004u3011u67e5u8be2u4ebau5458u4fe1u606f3e203ca3e">1.1.4【RC1.1.4S004】查询人员信息
<a id=1.1.4【RC1.1.4S004】查询人员信息> </a></a></li><li><a href="#115u3010rc115s005u3011u67e5u8be2u95e8u8bcau60a3u8005u5217u88680a3ca20id3d115u3010rc115s005u3011u67e5u8be2u95e8u8bcau60a3u8005u5217u88683e203ca3e">1.1.5【RC1.1.5S005】查询门诊患者列表
<a id=1.1.5【RC1.1.5S005】查询门诊患者列表> </a></a></li><li><a href="#116u3010rc116s006u3011u83b7u53d6u95e8u8bcau60a3u8005u4fe1u606f0a3ca20id3d116u3010rc116s006u3011u83b7u53d6u95e8u8bcau60a3u8005u4fe1u606f3e203ca3e">1.1.6【RC1.1.6S006】获取门诊患者信息
<a id=1.1.6【RC1.1.6S006】获取门诊患者信息> </a></a></li><li><a href="#117u3010rc117s221u3011u83b7u53d6u60a3u8005u51fau9662u4fe1u606f0a3ca20id3d117u3010rc117s221u3011u83b7u53d6u60a3u8005u51fau9662u4fe1u606f3e203ca3e">1.1.7【RC1.1.7S221】获取患者出院信息
<a id=1.1.7【RC1.1.7S221】获取患者出院信息> </a></a></li><li><a href="#117u3010rc117s007u3011u67e5u8be2u4f4fu9662u60a3u8005u5217u88680a3ca20id3d117u3010rc117s007u3011u67e5u8be2u4f4fu9662u60a3u8005u5217u88683e203ca3e">1.1.7【RC1.1.7S007】查询住院患者列表
<a id=1.1.7【RC1.1.7S007】查询住院患者列表> </a></a></li><li><a href="#118u3010rc118s008u3011u83b7u53d6u4f4fu9662u60a3u8005u4fe1u606f0a3ca20id3d118u3010rc118s008u3011u83b7u53d6u4f4fu9662u60a3u8005u4fe1u606f3e203ca3e">1.1.8【RC1.1.8S008】获取住院患者信息
<a id=1.1.8【RC1.1.8S008】获取住院患者信息> </a></a></li><li><a href="#119u3010rc119s009u3011u836fu54c1u8be6u7ec6u67e5u8be20a3ca20id3d119u3010rc119s009u3011u836fu54c1u8be6u7ec6u67e5u8be23e203ca3e">1.1.9【RC1.1.9S009】药品详细查询
<a id=1.1.9【RC1.1.9S009】药品详细查询> </a></a></li><li><a href="#1110u3010rc1110s010u3011u67e5u8be2u75c5u5e8au4fe1u606fu63a5u53e30a3ca20id3d1110u3010rc1110s010u3011u67e5u8be2u75c5u5e8au4fe1u606fu63a5u53e33e203ca3e">1.1.10【RC1.1.10S010】查询病床信息接口
<a id=1.1.10【RC1.1.10S010】查询病床信息接口> </a></a></li><li><a href="#1111u3010rc1111s011u3011u67e5u8be2u533bu9662u4fe1u606fu63a5u53e30a3ca20id3d1111u3010rc1111s011u3011u67e5u8be2u533bu9662u4fe1u606fu63a5u53e33e203ca3e">1.1.11【RC1.1.11S011】查询医院信息接口
<a id=1.1.11【RC1.1.11S011】查询医院信息接口> </a></a></li><li><a href="#1112u3010rc1112s012u3011u67e5u8be2u836fu54c1u7269u4ef7u9879u76eeu5217u88680a3ca20id3d1112u3010rc1112s012u3011u67e5u8be2u836fu54c1u7269u4ef7u9879u76eeu5217u88683e203ca3e">1.1.12【RC1.1.12S012】查询药品物价项目列表
<a id=1.1.12【RC1.1.12S012】查询药品物价项目列表> </a></a></li><li><a href="#1113u3010rc1113s013u3011u67e5u8be2u975eu836fu54c1u7269u4ef7u9879u76eeu5217u88680a3ca20id3d1113u3010rc1113s013u3011u67e5u8be2u975eu836fu54c1u7269u4ef7u9879u76eeu5217u88683e203ca3e">1.1.13【RC1.1.13S013】查询非药品物价项目列表
<a id=1.1.13【RC1.1.13S013】查询非药品物价项目列表> </a></a></li><li><a href="#1114u3010rc1114s014u3011u67e5u8be2u533bu751fu6709u6743u9650u7684u79d1u5ba40a3ca20id3d1114u3010rc1114s014u3011u67e5u8be2u533bu751fu6709u6743u9650u7684u79d1u5ba43e203ca3e">1.1.14【RC1.1.14S014】查询医生有权限的科室
<a id=1.1.14【RC1.1.14S014】查询医生有权限的科室> </a></a></li><li><a href="#1115u3010rc1115s203u3011u67e5u8be2u75c5u533au5185u6240u6709u5a74u513fu5217u88680a3ca20id3d1115u3010rc1115s203u3011u67e5u8be2u75c5u533au5185u6240u6709u5a74u513fu5217u88683e203ca3e">1.1.15【RC1.1.15S203】查询病区内所有婴儿列表
<a id=1.1.15【RC1.1.15S203】查询病区内所有婴儿列表> </a></a></li><li><a href="#1116u3010rc1116s231u3011u67e5u8be2u79d1u5ba4u5728u9662u4ebau65700a3ca20id3d1116u3010rc1116s231u3011u67e5u8be2u79d1u5ba4u5728u9662u4ebau65703e203ca3e">1.1.16【RC1.1.16S231】查询科室在院人数
<a id=1.1.16【RC1.1.16S231】查询科室在院人数> </a></a></li></ul></li><li><a href="#12u63a5u53e3u6570u636eu8131u654f">1.2接口数据脱敏</a><ul><li><a href="#local-111u3010rp111u3011u4fddu5b58u8131u654fu89c4u52190a3ca20id3dlocal-111u3010rp111u3011u4fddu5b58u8131u654fu89c4u52193e203ca3e">local-1.1.1【RP1.1.1】保存脱敏规则
<a id=local-1.1.1【RP1.1.1】保存脱敏规则> </a></a></li><li><a href="#local-112u3010rp112u3011u67e5u8be2u8131u654fu89c4u5219u4fe1u606fu5217u88680a3ca20id3dlocal-112u3010rp112u3011u67e5u8be2u8131u654fu89c4u5219u4fe1u606fu5217u88683e203ca3e">local-1.1.2【RP1.1.2】查询脱敏规则信息列表
<a id=local-1.1.2【RP1.1.2】查询脱敏规则信息列表> </a></a></li><li><a href="#local-113u3010rp113u3011u6e05u7a7au8131u654fu89c4u5219u4fe1u606f0a3ca20id3dlocal-113u3010rp113u3011u6e05u7a7au8131u654fu89c4u5219u4fe1u606f3e203ca3e">local-1.1.3【RP1.1.3】清空脱敏规则信息
<a id=local-1.1.3【RP1.1.3】清空脱敏规则信息> </a></a></li><li><a href="#local-114u3010rp114u3011u65b0u589eu8131u654fu57fau7840u6570u636e0a3ca20id3dlocal-114u3010rp114u3011u65b0u589eu8131u654fu57fau7840u6570u636e3e203ca3e">local-1.1.4【RP1.1.4】新增脱敏基础数据
<a id=local-1.1.4【RP1.1.4】新增脱敏基础数据> </a></a></li></ul></li><li><a href="#13u767bu5f55u670du52a1">1.3登录服务</a><ul><li><a href="#131u3010rc131s189u3011u533bu751fu767bu5f550a3ca20id3d131u3010rc131s189u3011u533bu751fu767bu5f553e203ca3e">1.3.1【RC1.3.1S189】医生登录
<a id=1.3.1【RC1.3.1S189】医生登录> </a></a></li></ul></li><li><a href="#u63a5u53e3u6570u636eu8131u654f">接口数据脱敏</a><ul><li><a href="#local-115u3010rp115u3011u6d4bu8bd5u8131u654fu51fdu65700a3ca20id3dlocal-115u3010rp115u3011u6d4bu8bd5u8131u654fu51fdu65703e203ca3e">local-1.1.5【RP1.1.5】测试脱敏函数
<a id=local-1.1.5【RP1.1.5】测试脱敏函数> </a></a></li></ul></li></ul></div>
          <div id="right" class="content-right">
           <h1 class="curproject-name"> curr-web </h1> 
 公共服务相关接口
<h1 id="11u67e5u8be2u670du52a1">1.1查询服务</h1>
<p></p>
<h2 id="111u3010rc111s001u3011u67e5u8be2u79d1u5ba4u5217u88680a3ca20id3d111u3010rc111s001u3011u67e5u8be2u79d1u5ba4u5217u88683e203ca3e">1.1.1【RC1.1.1S001】查询科室列表
<a id=1.1.1【RC1.1.1S001】查询科室列表> </a></h2>
<p></p>
<h3 id="">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/dept/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询科室列表</p>
<h3 id="-2">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>branchCode</td>
<td>否</td>
<td></td>
<td>院区编码</td>
</tr>
<tr>
<td>deptCode</td>
<td>否</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>deptType</td>
<td>否</td>
<td></td>
<td>科室类型</td>
</tr>
</tbody>
</table>
<h3 id="-3">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> alterMoney</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">警戒线</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> branchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院区编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> createDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">创建时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleBegin</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期开始</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleEnd</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期结束</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptAddress</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室地址</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptDesc</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室简介</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室英文</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptPro</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">特殊科室属性</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptProfCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室专业分类编码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室联系电话</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ext1Flag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志1</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> extFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志 －是否已经集中发送 0 未,1 已</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mediTime</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否挂号科室 0 假 1 真</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> remark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">备注</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> simpleName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室简称</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sortId</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">顺序号</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室拼音码</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> subBranchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">子院区编码</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tatdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否核算科室 0 假 1 真</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> userCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自定义码</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> validState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效性标志 1在用 0 停用 2 废弃</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室五笔码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"alterMoney":</span><span style="color: green;">0</span>,<span style="color: green;">"branchCode":</span><span style="color: green;">""</span>,<span style="color: green;">"createDate":</span><span style="color: green;">""</span>,<span style="color: green;">"cycleBegin":</span><span style="color: green;">0</span>,<span style="color: green;">"cycleEnd":</span><span style="color: green;">0</span>,<span style="color: green;">"deptAddress":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptDesc":</span><span style="color: green;">""</span>,<span style="color: green;">"deptEname":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"deptPro":</span><span style="color: green;">""</span>,<span style="color: green;">"deptProfCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptTel":</span><span style="color: green;">""</span>,<span style="color: green;">"ext1Flag":</span><span style="color: green;">""</span>,<span style="color: green;">"extFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"mediTime":</span><span style="color: green;">0</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"regdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"remark":</span><span style="color: green;">""</span>,<span style="color: green;">"simpleName":</span><span style="color: green;">""</span>,<span style="color: green;">"sortId":</span><span style="color: green;">0</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"subBranchCode":</span><span style="color: green;">""</span>,<span style="color: green;">"tatdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"userCode":</span><span style="color: green;">""</span>,<span style="color: green;">"validState":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="112u3010rc112s002u3011u83b7u53d6u79d1u5ba4u4fe1u606f0a3ca20id3d112u3010rc112s002u3011u83b7u53d6u79d1u5ba4u4fe1u606f3e203ca3e">1.1.2【RC1.1.2S002】获取科室信息
<a id=1.1.2【RC1.1.2S002】获取科室信息> </a></h2>
<p></p>
<h3 id="-4">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/dept/get/{deptId}</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
获取科室信息</p>
<h3 id="-5">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>deptId</td>
<td></td>
<td>科室编号</td>
</tr>
</tbody>
</table>
<h3 id="-6">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室信息</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> alterMoney</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">警戒线</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleBegin</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期开始</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleEnd</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期结束</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室英文</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptPro</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">特殊科室属性</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室类型</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ext1Flag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志1</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> extFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志 －是否已经集中发送 0 未,1 已</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mediTime</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否挂号科室 0 假 1 真</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> simpleName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室简称</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sortId</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">顺序号</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">拼音</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tatdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否核算科室 0 假 1 真</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> userCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自定义码</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> validState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效性标志 1在用 0 停用 2 废弃</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">五笔</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-2">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"alterMoney":</span><span style="color: green;">0</span>,<span style="color: green;">"cycleBegin":</span><span style="color: green;">0</span>,<span style="color: green;">"cycleEnd":</span><span style="color: green;">0</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptEname":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"deptPro":</span><span style="color: green;">""</span>,<span style="color: green;">"deptType":</span><span style="color: green;">""</span>,<span style="color: green;">"ext1Flag":</span><span style="color: green;">""</span>,<span style="color: green;">"extFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"mediTime":</span><span style="color: green;">0</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"regdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"simpleName":</span><span style="color: green;">""</span>,<span style="color: green;">"sortId":</span><span style="color: green;">0</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"tatdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"userCode":</span><span style="color: green;">""</span>,<span style="color: green;">"validState":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="113u3010rc113s003u3011u67e5u8be2u4ebau5458u5217u88680a3ca20id3d113u3010rc113s003u3011u67e5u8be2u4ebau5458u5217u88683e203ca3e">1.1.3【RC1.1.3S003】查询人员列表
<a id=1.1.3【RC1.1.3S003】查询人员列表> </a></h2>
<p></p>
<h3 id="-7">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/employee/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询(工作)人员列表</p>
<h3 id="-8">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>emplCode</td>
<td>否</td>
<td></td>
<td>员工代码</td>
</tr>
<tr>
<td>emplName</td>
<td>否</td>
<td></td>
<td>员工姓名</td>
</tr>
<tr>
<td>spellCode</td>
<td>否</td>
<td></td>
<td>拼音码</td>
</tr>
<tr>
<td>wbCode</td>
<td>否</td>
<td></td>
<td>五笔</td>
</tr>
<tr>
<td>sexCode</td>
<td>否</td>
<td></td>
<td>性别</td>
</tr>
<tr>
<td>birthday</td>
<td>否</td>
<td></td>
<td>出生日期</td>
</tr>
<tr>
<td>posiCode</td>
<td>否</td>
<td></td>
<td>职务代号</td>
</tr>
<tr>
<td>levlCode</td>
<td>否</td>
<td></td>
<td>职级代号</td>
</tr>
<tr>
<td>educationCode</td>
<td>否</td>
<td></td>
<td>学历</td>
</tr>
<tr>
<td>idenno</td>
<td>否</td>
<td></td>
<td>身份证号</td>
</tr>
<tr>
<td>deptCode</td>
<td>否</td>
<td></td>
<td>所属科室号</td>
</tr>
<tr>
<td>nurseCellCode</td>
<td>否</td>
<td></td>
<td>所属护理站</td>
</tr>
<tr>
<td>emplType</td>
<td>否</td>
<td></td>
<td>人员类型</td>
</tr>
<tr>
<td>expertFlag</td>
<td>否</td>
<td></td>
<td>是否专家</td>
</tr>
</tbody>
</table>
<h3 id="-9">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生日期</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">所属科室号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> educationCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学历</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工代码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工姓名</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">人员类型</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> expertFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否专家</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idenno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> levelName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职级名称</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> levlCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职级代号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">所属护理站</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> posiCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职务代号</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sexCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specialAbility</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医生擅长</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">拼音码</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">五笔</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-3">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"educationCode":</span><span style="color: green;">""</span>,<span style="color: green;">"emplCode":</span><span style="color: green;">""</span>,<span style="color: green;">"emplName":</span><span style="color: green;">""</span>,<span style="color: green;">"emplType":</span><span style="color: green;">""</span>,<span style="color: green;">"expertFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"idenno":</span><span style="color: green;">""</span>,<span style="color: green;">"levelName":</span><span style="color: green;">""</span>,<span style="color: green;">"levlCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"posiCode":</span><span style="color: green;">""</span>,<span style="color: green;">"sexCode":</span><span style="color: green;">""</span>,<span style="color: green;">"specialAbility":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="114u3010rc114s004u3011u67e5u8be2u4ebau5458u4fe1u606f0a3ca20id3d114u3010rc114s004u3011u67e5u8be2u4ebau5458u4fe1u606f3e203ca3e">1.1.4【RC1.1.4S004】查询人员信息
<a id=1.1.4【RC1.1.4S004】查询人员信息> </a></h2>
<p></p>
<h3 id="-10">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/employee/get/{emplId}</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询人员详细信息</p>
<h3 id="-11">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>emplId</td>
<td></td>
<td>人员编号</td>
</tr>
</tbody>
</table>
<h3 id="-12">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">查询(工作)人员详细信息出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生日期</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">所属科室号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> educationCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学历</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工代码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工姓名</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">人员类型</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> expertFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">人员类型</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ext1Flag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志1</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> extFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idenno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> levlCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职级代号</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">所属护理站</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> posiCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职务代号</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> salaryId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">工资号</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sexCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sortId</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">顺序号</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工姓名拼音码</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> userCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">自定义码</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> validState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效性标志 1 有效 0 停用 2 废弃</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工姓名五笔码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-4">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"educationCode":</span><span style="color: green;">""</span>,<span style="color: green;">"emplCode":</span><span style="color: green;">""</span>,<span style="color: green;">"emplName":</span><span style="color: green;">""</span>,<span style="color: green;">"emplType":</span><span style="color: green;">""</span>,<span style="color: green;">"expertFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"ext1Flag":</span><span style="color: green;">""</span>,<span style="color: green;">"extFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"idenno":</span><span style="color: green;">""</span>,<span style="color: green;">"levlCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"posiCode":</span><span style="color: green;">""</span>,<span style="color: green;">"salaryId":</span><span style="color: green;">""</span>,<span style="color: green;">"sexCode":</span><span style="color: green;">""</span>,<span style="color: green;">"sortId":</span><span style="color: green;">0</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"userCode":</span><span style="color: green;">""</span>,<span style="color: green;">"validState":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="115u3010rc115s005u3011u67e5u8be2u95e8u8bcau60a3u8005u5217u88680a3ca20id3d115u3010rc115s005u3011u67e5u8be2u95e8u8bcau60a3u8005u5217u88683e203ca3e">1.1.5【RC1.1.5S005】查询门诊患者列表
<a id=1.1.5【RC1.1.5S005】查询门诊患者列表> </a></h2>
<p></p>
<h3 id="-13">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/out-patient/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询门诊患者列表</p>
<h3 id="-14">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>patientId</td>
<td>否</td>
<td></td>
<td>患者ID唯一标志</td>
</tr>
<tr>
<td>markNo</td>
<td>否</td>
<td></td>
<td>就诊卡号</td>
</tr>
<tr>
<td>markType</td>
<td>否</td>
<td></td>
<td>卡类型</td>
</tr>
<tr>
<td>idNo</td>
<td>否</td>
<td></td>
<td>身份证号</td>
</tr>
<tr>
<td>tel</td>
<td>否</td>
<td></td>
<td>电话号码</td>
</tr>
<tr>
<td>name</td>
<td>否</td>
<td></td>
<td>患者姓名</td>
</tr>
<tr>
<td>medicalCardNo</td>
<td>否</td>
<td></td>
<td>医保卡号</td>
</tr>
</tbody>
</table>
<h3 id="-15">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> address</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">地址</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生日</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者唯一ID</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sex</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">电话号码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-5">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"address":</span><span style="color: green;">""</span>,<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"idNo":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"sex":</span><span style="color: green;">""</span>,<span style="color: green;">"tel":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="116u3010rc116s006u3011u83b7u53d6u95e8u8bcau60a3u8005u4fe1u606f0a3ca20id3d116u3010rc116s006u3011u83b7u53d6u95e8u8bcau60a3u8005u4fe1u606f3e203ca3e">1.1.6【RC1.1.6S006】获取门诊患者信息
<a id=1.1.6【RC1.1.6S006】获取门诊患者信息> </a></h2>
<p></p>
<h3 id="-16">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/out-patient/get/{outPatientId}</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
获取门诊患者信息</p>
<h3 id="-17">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>outPatientId</td>
<td></td>
<td>患者编号</td>
</tr>
</tbody>
</table>
<h3 id="-18">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">门诊患者信息出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> actAmt</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">帐户总额</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> actCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">帐户密码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> anaphyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药物过敏</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> area</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">区</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> area1</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地区</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> area2</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口地址区</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> area3</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">籍贯区</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> areaCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> arrearSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">欠费金额</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> arrearTimes</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">欠费次数</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生日期</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bloodCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">血型</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者唯一ID</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> caseNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病案号</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> city</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">城市</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> city1</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地城市</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> city2</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口地址城市</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> city3</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">籍贯城市</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> counCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">国籍</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> disobyCnt</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">违约次数</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> district</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">籍贯</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> email</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">email地址</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工编码</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emrPatid</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">emr患者号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> endDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结束日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> feeKind</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">算法类别</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> firSeeDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">初诊日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> framt</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗费用</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hepatitisFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">重要疾病</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> home</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口或家庭所在</span></td><td key=5></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeDoorNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭住址门牌号</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭电话</span></td><td key=5></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeZip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口或家庭邮政编码</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> icCardno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">电脑号</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idcardtype</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">证件类型</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idenno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inhosSource</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院来源</span></td><td key=5></td></tr><tr key=0-1-37><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inhosTimes</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">民族</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-38><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> insuranceId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">保险公司编码</span></td><td key=5></td></tr><tr key=0-1-39><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> insuranceName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">保险公司名称</span></td><td key=5></td></tr><tr key=0-1-40><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isEncryptname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否加密姓名</span></td><td key=5></td></tr><tr key=0-1-41><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isTreatment</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否急诊</span></td><td key=5></td></tr><tr key=0-1-42><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isValid</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否有效</span></td><td key=5></td></tr><tr key=0-1-43><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> lactSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">上期帐户余额</span></td><td key=5></td></tr><tr key=0-1-44><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> lbankSum</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">上期银行余额</span></td><td key=5></td></tr><tr key=0-1-45><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> lihosDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最近住院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-46><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanAdd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人住址</span></td><td key=5></td></tr><tr key=0-1-47><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanDoorNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人地址门牌号</span></td><td key=5></td></tr><tr key=0-1-48><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人姓名</span></td><td key=5></td></tr><tr key=0-1-49><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人电话</span></td><td key=5></td></tr><tr key=0-1-50><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> louthosDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最近出院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-51><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> lregDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">最近挂号日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-52><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mari</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">婚姻状况</span></td><td key=5></td></tr><tr key=0-1-53><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">备注</span></td><td key=5></td></tr><tr key=0-1-54><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mcardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗证号</span></td><td key=5></td></tr><tr key=0-1-55><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> montherName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲姓名</span></td><td key=5></td></tr><tr key=0-1-56><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-57><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nationCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">民族</span></td><td key=5></td></tr><tr key=0-1-58><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> normalname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">密文</span></td><td key=5></td></tr><tr key=0-1-59><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> oldCardno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">旧卡号</span></td><td key=5></td></tr><tr key=0-1-60><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-61><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-62><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同代码</span></td><td key=5></td></tr><tr key=0-1-63><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-64><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> paykindCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别</span></td><td key=5></td></tr><tr key=0-1-65><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> paykindName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别名称</span></td><td key=5></td></tr><tr key=0-1-66><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> profCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职业</span></td><td key=5></td></tr><tr key=0-1-67><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> profName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职业名称</span></td><td key=5></td></tr><tr key=0-1-68><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> province</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">省份</span></td><td key=5></td></tr><tr key=0-1-69><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> province1</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地省份</span></td><td key=5></td></tr><tr key=0-1-70><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> province2</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口地址省份</span></td><td key=5></td></tr><tr key=0-1-71><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> province3</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">籍贯省份</span></td><td key=5></td></tr><tr key=0-1-72><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> relaCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人关系</span></td><td key=5></td></tr><tr key=0-1-73><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> road</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">街道</span></td><td key=5></td></tr><tr key=0-1-74><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> road1</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">户口地址街道</span></td><td key=5></td></tr><tr key=0-1-75><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sexCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-76><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">拼音码</span></td><td key=5></td></tr><tr key=0-1-77><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> vipFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否Vip</span></td><td key=5></td></tr><tr key=0-1-78><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">五笔</span></td><td key=5></td></tr><tr key=0-1-79><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workHome</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">工作单位</span></td><td key=5></td></tr><tr key=0-1-80><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位电话</span></td><td key=5></td></tr><tr key=0-1-81><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workZip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位邮编</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-6">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"actAmt":</span><span style="color: green;">0</span>,<span style="color: green;">"actCode":</span><span style="color: green;">""</span>,<span style="color: green;">"anaphyFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"area":</span><span style="color: green;">""</span>,<span style="color: green;">"area1":</span><span style="color: green;">""</span>,<span style="color: green;">"area2":</span><span style="color: green;">""</span>,<span style="color: green;">"area3":</span><span style="color: green;">""</span>,<span style="color: green;">"areaCode":</span><span style="color: green;">""</span>,<span style="color: green;">"arrearSum":</span><span style="color: green;">0</span>,<span style="color: green;">"arrearTimes":</span><span style="color: green;">0</span>,<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"bloodCode":</span><span style="color: green;">""</span>,<span style="color: green;">"cardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"caseNo":</span><span style="color: green;">""</span>,<span style="color: green;">"city":</span><span style="color: green;">""</span>,<span style="color: green;">"city1":</span><span style="color: green;">""</span>,<span style="color: green;">"city2":</span><span style="color: green;">""</span>,<span style="color: green;">"city3":</span><span style="color: green;">""</span>,<span style="color: green;">"counCode":</span><span style="color: green;">""</span>,<span style="color: green;">"disobyCnt":</span><span style="color: green;">0</span>,<span style="color: green;">"district":</span><span style="color: green;">""</span>,<span style="color: green;">"email":</span><span style="color: green;">""</span>,<span style="color: green;">"emplCode":</span><span style="color: green;">""</span>,<span style="color: green;">"emrPatid":</span><span style="color: green;">0</span>,<span style="color: green;">"endDate":</span><span style="color: green;">""</span>,<span style="color: green;">"feeKind":</span><span style="color: green;">""</span>,<span style="color: green;">"firSeeDate":</span><span style="color: green;">""</span>,<span style="color: green;">"framt":</span><span style="color: green;">0</span>,<span style="color: green;">"hepatitisFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"home":</span><span style="color: green;">""</span>,<span style="color: green;">"homeDoorNo":</span><span style="color: green;">""</span>,<span style="color: green;">"homeTel":</span><span style="color: green;">""</span>,<span style="color: green;">"homeZip":</span><span style="color: green;">""</span>,<span style="color: green;">"icCardno":</span><span style="color: green;">""</span>,<span style="color: green;">"idcardtype":</span><span style="color: green;">""</span>,<span style="color: green;">"idenno":</span><span style="color: green;">""</span>,<span style="color: green;">"inhosSource":</span><span style="color: green;">""</span>,<span style="color: green;">"inhosTimes":</span><span style="color: green;">0</span>,<span style="color: green;">"insuranceId":</span><span style="color: green;">""</span>,<span style="color: green;">"insuranceName":</span><span style="color: green;">""</span>,<span style="color: green;">"isEncryptname":</span><span style="color: green;">""</span>,<span style="color: green;">"isTreatment":</span><span style="color: green;">""</span>,<span style="color: green;">"isValid":</span><span style="color: green;">""</span>,<span style="color: green;">"lactSum":</span><span style="color: green;">0</span>,<span style="color: green;">"lbankSum":</span><span style="color: green;">0</span>,<span style="color: green;">"lihosDate":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanAdd":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanDoorNo":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanName":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanTel":</span><span style="color: green;">""</span>,<span style="color: green;">"louthosDate":</span><span style="color: green;">""</span>,<span style="color: green;">"lregDate":</span><span style="color: green;">""</span>,<span style="color: green;">"mari":</span><span style="color: green;">""</span>,<span style="color: green;">"mark":</span><span style="color: green;">""</span>,<span style="color: green;">"mcardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"montherName":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"nationCode":</span><span style="color: green;">""</span>,<span style="color: green;">"normalname":</span><span style="color: green;">""</span>,<span style="color: green;">"oldCardno":</span><span style="color: green;">""</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"paykindCode":</span><span style="color: green;">""</span>,<span style="color: green;">"paykindName":</span><span style="color: green;">""</span>,<span style="color: green;">"profCode":</span><span style="color: green;">""</span>,<span style="color: green;">"profName":</span><span style="color: green;">""</span>,<span style="color: green;">"province":</span><span style="color: green;">""</span>,<span style="color: green;">"province1":</span><span style="color: green;">""</span>,<span style="color: green;">"province2":</span><span style="color: green;">""</span>,<span style="color: green;">"province3":</span><span style="color: green;">""</span>,<span style="color: green;">"relaCode":</span><span style="color: green;">""</span>,<span style="color: green;">"road":</span><span style="color: green;">""</span>,<span style="color: green;">"road1":</span><span style="color: green;">""</span>,<span style="color: green;">"sexCode":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"vipFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>,<span style="color: green;">"workHome":</span><span style="color: green;">""</span>,<span style="color: green;">"workTel":</span><span style="color: green;">""</span>,<span style="color: green;">"workZip":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="117u3010rc117s221u3011u83b7u53d6u60a3u8005u51fau9662u4fe1u606f0a3ca20id3d117u3010rc117s221u3011u83b7u53d6u60a3u8005u51fau9662u4fe1u606f3e203ca3e">1.1.7【RC1.1.7S221】获取患者出院信息
<a id=1.1.7【RC1.1.7S221】获取患者出院信息> </a></h2>
<p></p>
<h3 id="-19">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/getOut</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
获取患者出院信息</p>
<h3 id="-20">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>cardNo</td>
<td>否</td>
<td></td>
<td>就诊卡号</td>
</tr>
<tr>
<td>inpatientNo</td>
<td>否</td>
<td></td>
<td>患者住院流水号</td>
</tr>
</tbody>
</table>
<h3 id="-21">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> age</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">年龄</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedNum</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生日期</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cyyz</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院医嘱</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> houseDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">经管医生</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院日期</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inDiagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院诊断</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区名称</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> outDiagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院诊断</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院号</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sex</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> zg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">转归</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> zljg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院经过</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> zyqk</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院时情况</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-7">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"age":</span><span style="color: green;">""</span>,<span style="color: green;">"bedNum":</span><span style="color: green;">""</span>,<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"cardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"cyyz":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"houseDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"inDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inDiagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"outDiagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"patientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"sex":</span><span style="color: green;">""</span>,<span style="color: green;">"zg":</span><span style="color: green;">""</span>,<span style="color: green;">"zljg":</span><span style="color: green;">""</span>,<span style="color: green;">"zyqk":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="117u3010rc117s007u3011u67e5u8be2u4f4fu9662u60a3u8005u5217u88680a3ca20id3d117u3010rc117s007u3011u67e5u8be2u4f4fu9662u60a3u8005u5217u88683e203ca3e">1.1.7【RC1.1.7S007】查询住院患者列表
<a id=1.1.7【RC1.1.7S007】查询住院患者列表> </a></h2>
<p></p>
<h3 id="-22">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询在院患者列表</p>
<h3 id="-23">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>cardNo</td>
<td>否</td>
<td></td>
<td>就诊卡号</td>
</tr>
<tr>
<td>deptCode</td>
<td>否</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>nurseCellCode</td>
<td>否</td>
<td></td>
<td>护理单元编码</td>
</tr>
<tr>
<td>idenno</td>
<td>否</td>
<td></td>
<td>身份证号</td>
</tr>
<tr>
<td>tel</td>
<td>否</td>
<td></td>
<td>电话号码</td>
</tr>
<tr>
<td>patientNo</td>
<td>否</td>
<td></td>
<td>住院号</td>
</tr>
<tr>
<td>patientId</td>
<td>否</td>
<td></td>
<td>emr患者ID</td>
</tr>
<tr>
<td>patientName</td>
<td>否</td>
<td></td>
<td>患者姓名</td>
</tr>
<tr>
<td>inDate</td>
<td>否</td>
<td></td>
<td>入院时间 YYYY-MM-DD HH24:MI:SS</td>
</tr>
<tr>
<td>patientState</td>
<td>否</td>
<td></td>
<td>患者在院状态</td>
</tr>
<tr>
<td>outDate</td>
<td>否</td>
<td></td>
<td>出院时间</td>
</tr>
<tr>
<td>beginTime</td>
<td>否</td>
<td></td>
<td>筛选时间begin</td>
</tr>
<tr>
<td>endTime</td>
<td>否</td>
<td></td>
<td>筛选时间end</td>
</tr>
<tr>
<td>pageSize</td>
<td>否</td>
<td></td>
<td>数据分页条数</td>
</tr>
<tr>
<td>pageNum</td>
<td>否</td>
<td></td>
<td>分页页码</td>
</tr>
<tr>
<td>pageFlag</td>
<td>否</td>
<td></td>
<td>分页flag</td>
</tr>
<tr>
<td>outBeginDate</td>
<td>否</td>
<td></td>
<td>开始时间（出院）</td>
</tr>
<tr>
<td>outEndDate</td>
<td>否</td>
<td></td>
<td>结束时间（出院）</td>
</tr>
</tbody>
</table>
<h3 id="-24">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> endRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasNextPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hasPreviousPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isFirstPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isLastPage</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> list</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-5-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> age</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">年龄</span></td><td key=5></td></tr><tr key=0-1-5-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> anaphyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否药物过敏</span></td><td key=5></td></tr><tr key=0-1-5-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balanceCost</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用金额(已结)</span></td><td key=5></td></tr><tr key=0-1-5-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> balancePrepay</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金额(已结)</span></td><td key=5></td></tr><tr key=0-1-5-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> bedNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床号</span></td><td key=5></td></tr><tr key=0-1-5-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> branchCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院区编码</span></td><td key=5></td></tr><tr key=0-1-5-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> cardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者ID</span></td><td key=5></td></tr><tr key=0-1-5-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chiefComplaint</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主诉</span></td><td key=5></td></tr><tr key=0-1-5-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chiefDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师代码(主任)</span></td><td key=5></td></tr><tr key=0-1-5-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> chiefDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师姓名(主任)</span></td><td key=5></td></tr><tr key=0-1-5-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> criticalFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">危重情况</span></td><td key=5></td></tr><tr key=0-1-5-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室代码</span></td><td key=5></td></tr><tr key=0-1-5-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室</span></td><td key=5></td></tr><tr key=0-1-5-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">诊断</span></td><td key=5></td></tr><tr key=0-1-5-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> diagnose2</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">诊断2</span></td><td key=5></td></tr><tr key=0-1-5-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> directorDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主治医生编码</span></td><td key=5></td></tr><tr key=0-1-5-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> directorDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主治医生姓名</span></td><td key=5></td></tr><tr key=0-1-5-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dutyNurseCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">责任护士编码</span></td><td key=5></td></tr><tr key=0-1-5-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dutyNurseName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">责任护士姓名</span></td><td key=5></td></tr><tr key=0-1-5-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> freeCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">余额</span></td><td key=5></td></tr><tr key=0-1-5-21><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> height</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身高</span></td><td key=5></td></tr><tr key=0-1-5-22><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> homeTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">电话号码</span></td><td key=5></td></tr><tr key=0-1-5-23><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> idenno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-5-24><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inAge</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院年龄</span></td><td key=5></td></tr><tr key=0-1-5-25><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inAvenuei</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者来源</span></td><td key=5></td></tr><tr key=0-1-5-26><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-27><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inDays</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院天数</span></td><td key=5></td></tr><tr key=0-1-5-28><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院科室编码</span></td><td key=5></td></tr><tr key=0-1-5-29><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院科室名称</span></td><td key=5></td></tr><tr key=0-1-5-30><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inTimes</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院次数</span></td><td key=5></td></tr><tr key=0-1-5-31><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-5-32><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> isNewBorn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否新生儿</span></td><td key=5></td></tr><tr key=0-1-5-33><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> linkmanAdd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人地址</span></td><td key=5></td></tr><tr key=0-1-5-34><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> linkmanName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人姓名</span></td><td key=5></td></tr><tr key=0-1-5-35><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> linkmanTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人电话</span></td><td key=5></td></tr><tr key=0-1-5-36><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> mainDiagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主诊断</span></td><td key=5></td></tr><tr key=0-1-5-37><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编号</span></td><td key=5></td></tr><tr key=0-1-5-38><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区名称</span></td><td key=5></td></tr><tr key=0-1-5-39><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> nursingLevel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护理级别</span></td><td key=5></td></tr><tr key=0-1-5-40><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> outDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5-41><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> outDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院科室编码</span></td><td key=5></td></tr><tr key=0-1-5-42><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> outDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院科室名称</span></td><td key=5></td></tr><tr key=0-1-5-43><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位</span></td><td key=5></td></tr><tr key=0-1-5-44><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-5-45><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者ID</span></td><td key=5></td></tr><tr key=0-1-5-46><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> patientName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名</span></td><td key=5></td></tr><tr key=0-1-5-47><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院号</span></td><td key=5></td></tr><tr key=0-1-5-48><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> patientState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者状态</span></td><td key=5></td></tr><tr key=0-1-5-49><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> paykindCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用类别</span></td><td key=5></td></tr><tr key=0-1-5-50><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> paykindName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用类别名称</span></td><td key=5></td></tr><tr key=0-1-5-51><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> prepayCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金</span></td><td key=5></td></tr><tr key=0-1-5-52><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> presentIllness</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">现病史</span></td><td key=5></td></tr><tr key=0-1-5-53><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> previousHistory</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">既往史</span></td><td key=5></td></tr><tr key=0-1-5-54><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> residencyDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">经治医生编码</span></td><td key=5></td></tr><tr key=0-1-5-55><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> residencyDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">经治医生姓名</span></td><td key=5></td></tr><tr key=0-1-5-56><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ryDiagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院诊断</span></td><td key=5></td></tr><tr key=0-1-5-57><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sexCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-5-58><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">总费用</span></td><td key=5></td></tr><tr key=0-1-5-59><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> weight</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">体重</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateFirstPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigateLastPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatePages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> navigatepageNums</span></td><td key=1><span>integer []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>integer</span></p></td></tr><tr key=array-1353><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nextPage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageNum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pageSize</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pages</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prePage</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> size</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> startRow</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> total</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int64</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-8">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"endRow":</span><span style="color: green;">0</span>,<span style="color: green;">"hasNextPage":</span><span style="color: green;">false</span>,<span style="color: green;">"hasPreviousPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isFirstPage":</span><span style="color: green;">false</span>,<span style="color: green;">"isLastPage":</span><span style="color: green;">false</span>,<span style="color: green;">"list":</span>[{<span style="color: green;">"age":</span><span style="color: green;">""</span>,<span style="color: green;">"anaphyFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"balanceCost":</span><span style="color: green;">0</span>,<span style="color: green;">"balancePrepay":</span><span style="color: green;">0</span>,<span style="color: green;">"bedNo":</span><span style="color: green;">""</span>,<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"branchCode":</span><span style="color: green;">""</span>,<span style="color: green;">"cardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"chiefComplaint":</span><span style="color: green;">""</span>,<span style="color: green;">"chiefDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"chiefDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"criticalFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"diagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"diagnose2":</span><span style="color: green;">""</span>,<span style="color: green;">"directorDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"directorDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"dutyNurseCode":</span><span style="color: green;">""</span>,<span style="color: green;">"dutyNurseName":</span><span style="color: green;">""</span>,<span style="color: green;">"freeCost":</span><span style="color: green;">""</span>,<span style="color: green;">"height":</span><span style="color: green;">""</span>,<span style="color: green;">"homeTel":</span><span style="color: green;">""</span>,<span style="color: green;">"idenno":</span><span style="color: green;">""</span>,<span style="color: green;">"inAge":</span><span style="color: green;">""</span>,<span style="color: green;">"inAvenuei":</span><span style="color: green;">""</span>,<span style="color: green;">"inDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inDays":</span><span style="color: green;">""</span>,<span style="color: green;">"inDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"inDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"inTimes":</span><span style="color: green;">""</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"isNewBorn":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanAdd":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanName":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanTel":</span><span style="color: green;">""</span>,<span style="color: green;">"mainDiagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"nursingLevel":</span><span style="color: green;">""</span>,<span style="color: green;">"outDate":</span><span style="color: green;">""</span>,<span style="color: green;">"outDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"outDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"patientState":</span><span style="color: green;">""</span>,<span style="color: green;">"paykindCode":</span><span style="color: green;">""</span>,<span style="color: green;">"paykindName":</span><span style="color: green;">""</span>,<span style="color: green;">"prepayCost":</span><span style="color: green;">""</span>,<span style="color: green;">"presentIllness":</span><span style="color: green;">""</span>,<span style="color: green;">"previousHistory":</span><span style="color: green;">""</span>,<span style="color: green;">"residencyDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"residencyDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"ryDiagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"sexCode":</span><span style="color: green;">""</span>,<span style="color: green;">"totCost":</span><span style="color: green;">""</span>,<span style="color: green;">"weight":</span><span style="color: green;">""</span>}],<span style="color: green;">"navigateFirstPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigateLastPage":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatePages":</span><span style="color: green;">0</span>,<span style="color: green;">"navigatepageNums":</span>[<span style="color: green;">null</span>],<span style="color: green;">"nextPage":</span><span style="color: green;">0</span>,<span style="color: green;">"pageNum":</span><span style="color: green;">0</span>,<span style="color: green;">"pageSize":</span><span style="color: green;">0</span>,<span style="color: green;">"pages":</span><span style="color: green;">0</span>,<span style="color: green;">"prePage":</span><span style="color: green;">0</span>,<span style="color: green;">"size":</span><span style="color: green;">0</span>,<span style="color: green;">"startRow":</span><span style="color: green;">0</span>,<span style="color: green;">"total":</span><span style="color: green;">0</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="118u3010rc118s008u3011u83b7u53d6u4f4fu9662u60a3u8005u4fe1u606f0a3ca20id3d118u3010rc118s008u3011u83b7u53d6u4f4fu9662u60a3u8005u4fe1u606f3e203ca3e">1.1.8【RC1.1.8S008】获取住院患者信息
<a id=1.1.8【RC1.1.8S008】获取住院患者信息> </a></h2>
<p></p>
<h3 id="-25">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/get/{inpatientNo}</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
住院患者详细信息查询</p>
<h3 id="-26">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>inpatientNo</td>
<td></td>
<td>患者编号</td>
</tr>
</tbody>
</table>
<h3 id="-27">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院患者详细信息出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> anaphyFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">过敏标志</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床号</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthArea</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地名称</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生日</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bloodCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">血型编码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bloodDress</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">血压</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者CARDNO</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> chargeDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师代码(主治)</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> chargeDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师姓名(主治)</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> chiefDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师代码(主任)</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> chiefDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师姓名(主任)</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> counCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">国籍</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> criticalFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病情标志</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室代码</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dist</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">籍贯</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dutyNurseCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护士代码(责任)</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dutyNurseName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护士姓名(责任)</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> freeCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">余额(未结)</span></td><td key=5></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> genderCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> height</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身高</span></td><td key=5></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hepatitisFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">重大疾病标志</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hisPatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者住院流水号</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeAdd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭地址</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭电话</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> homeZip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭邮编</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> houseDocCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师代码(住院)</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> houseDocName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医师姓名(住院)</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> idenno</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身份证号</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inAvenue</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院途径</span></td><td key=5></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inCircs</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院情况</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inSource</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院来源</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院状态</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inTimes</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院次数</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> isNewBorn</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否新生儿</span></td><td key=5></td></tr><tr key=0-1-36><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanAdd</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人地址</span></td><td key=5></td></tr><tr key=0-1-37><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人姓名</span></td><td key=5></td></tr><tr key=0-1-38><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> linkmanTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人电话</span></td><td key=5></td></tr><tr key=0-1-39><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mainDiagnose</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">主诊断</span></td><td key=5></td></tr><tr key=0-1-40><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mari</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">婚姻状况</span></td><td key=5></td></tr><tr key=0-1-41><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mcardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医疗证号</span></td><td key=5></td></tr><tr key=0-1-42><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> medicalType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者合同类型</span></td><td key=5></td></tr><tr key=0-1-43><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-44><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nationCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">民族</span></td><td key=5></td></tr><tr key=0-1-45><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护理单元代码</span></td><td key=5></td></tr><tr key=0-1-46><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> nurseCellName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护理单元名称</span></td><td key=5></td></tr><tr key=0-1-47><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> outDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-48><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pactCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同代码</span></td><td key=5></td></tr><tr key=0-1-49><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> pactName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">合同单位名称</span></td><td key=5></td></tr><tr key=0-1-50><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">EMR患者ID</span></td><td key=5></td></tr><tr key=0-1-51><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> patientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者住院号</span></td><td key=5></td></tr><tr key=0-1-52><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> paykindCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">结算类别</span></td><td key=5></td></tr><tr key=0-1-53><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prepayCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">预交金</span></td><td key=5></td></tr><tr key=0-1-54><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> profCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职业代码</span></td><td key=5></td></tr><tr key=0-1-55><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> profName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职业名称</span></td><td key=5></td></tr><tr key=0-1-56><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> relaCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人编码关系</span></td><td key=5></td></tr><tr key=0-1-57><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> relaName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">联系人关系</span></td><td key=5></td></tr><tr key=0-1-58><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">患者姓名拼音码</span></td><td key=5></td></tr><tr key=0-1-59><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tend</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">护理级别</span></td><td key=5></td></tr><tr key=0-1-60><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> totCost</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">费用汇总</span></td><td key=5></td></tr><tr key=0-1-61><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> weight</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">体重</span></td><td key=5></td></tr><tr key=0-1-62><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位名称</span></td><td key=5></td></tr><tr key=0-1-63><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workTel</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位电话</span></td><td key=5></td></tr><tr key=0-1-64><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> workZip</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位邮编</span></td><td key=5></td></tr><tr key=0-1-65><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> zg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">转归标记</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-9">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"anaphyFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"bedNo":</span><span style="color: green;">""</span>,<span style="color: green;">"birthArea":</span><span style="color: green;">""</span>,<span style="color: green;">"birthDate":</span><span style="color: green;">""</span>,<span style="color: green;">"bloodCode":</span><span style="color: green;">""</span>,<span style="color: green;">"bloodDress":</span><span style="color: green;">""</span>,<span style="color: green;">"cardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"chargeDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"chiefDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"chiefDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"counCode":</span><span style="color: green;">""</span>,<span style="color: green;">"criticalFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"dist":</span><span style="color: green;">""</span>,<span style="color: green;">"dutyNurseCode":</span><span style="color: green;">""</span>,<span style="color: green;">"dutyNurseName":</span><span style="color: green;">""</span>,<span style="color: green;">"freeCost":</span><span style="color: green;">""</span>,<span style="color: green;">"genderCode":</span><span style="color: green;">""</span>,<span style="color: green;">"height":</span><span style="color: green;">""</span>,<span style="color: green;">"hepatitisFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"hisPatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"homeAdd":</span><span style="color: green;">""</span>,<span style="color: green;">"homeTel":</span><span style="color: green;">""</span>,<span style="color: green;">"homeZip":</span><span style="color: green;">""</span>,<span style="color: green;">"houseDocCode":</span><span style="color: green;">""</span>,<span style="color: green;">"houseDocName":</span><span style="color: green;">""</span>,<span style="color: green;">"idenno":</span><span style="color: green;">""</span>,<span style="color: green;">"inAvenue":</span><span style="color: green;">""</span>,<span style="color: green;">"inCircs":</span><span style="color: green;">""</span>,<span style="color: green;">"inDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inSource":</span><span style="color: green;">""</span>,<span style="color: green;">"inState":</span><span style="color: green;">""</span>,<span style="color: green;">"inTimes":</span><span style="color: green;">""</span>,<span style="color: green;">"isNewBorn":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanAdd":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanName":</span><span style="color: green;">""</span>,<span style="color: green;">"linkmanTel":</span><span style="color: green;">""</span>,<span style="color: green;">"mainDiagnose":</span><span style="color: green;">""</span>,<span style="color: green;">"mari":</span><span style="color: green;">""</span>,<span style="color: green;">"mcardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"medicalType":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"nationCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"nurseCellName":</span><span style="color: green;">""</span>,<span style="color: green;">"outDate":</span><span style="color: green;">""</span>,<span style="color: green;">"pactCode":</span><span style="color: green;">""</span>,<span style="color: green;">"pactName":</span><span style="color: green;">""</span>,<span style="color: green;">"patientId":</span><span style="color: green;">""</span>,<span style="color: green;">"patientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"paykindCode":</span><span style="color: green;">""</span>,<span style="color: green;">"prepayCost":</span><span style="color: green;">""</span>,<span style="color: green;">"profCode":</span><span style="color: green;">""</span>,<span style="color: green;">"profName":</span><span style="color: green;">""</span>,<span style="color: green;">"relaCode":</span><span style="color: green;">""</span>,<span style="color: green;">"relaName":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"tend":</span><span style="color: green;">""</span>,<span style="color: green;">"totCost":</span><span style="color: green;">""</span>,<span style="color: green;">"weight":</span><span style="color: green;">""</span>,<span style="color: green;">"workName":</span><span style="color: green;">""</span>,<span style="color: green;">"workTel":</span><span style="color: green;">""</span>,<span style="color: green;">"workZip":</span><span style="color: green;">""</span>,<span style="color: green;">"zg":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="119u3010rc119s009u3011u836fu54c1u8be6u7ec6u67e5u8be20a3ca20id3d119u3010rc119s009u3011u836fu54c1u8be6u7ec6u67e5u8be23e203ca3e">1.1.9【RC1.1.9S009】药品详细查询
<a id=1.1.9【RC1.1.9S009】药品详细查询> </a></h2>
<p></p>
<h3 id="-28">基本信息</h3>
<p><strong>Path：</strong> /api/v1/curr/pharmaceutical/{drugCode}</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
药品详细查询</p>
<h3 id="-29">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>drugCode</td>
<td></td>
<td>药品编码</td>
</tr>
</tbody>
</table>
<h3 id="-30">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品详细查询出参</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> customCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品名自定义码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> formalCustom</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学名自定义码</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> formalName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学名</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> formalSpell</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学名拼音码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> formalWb</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">学名五笔码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> otherName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">别名</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> otherSpell</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">别名拼音码</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> otherWb</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">别名五笔码</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regularCustom</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">通用名自定义码</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regularName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">通用名</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regularSpell</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">通用名拼音码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regularWb</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">通用名五笔码</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品名拼音码</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tradeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品名称</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品名五笔码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-10">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"customCode":</span><span style="color: green;">""</span>,<span style="color: green;">"drugCode":</span><span style="color: green;">""</span>,<span style="color: green;">"formalCustom":</span><span style="color: green;">""</span>,<span style="color: green;">"formalName":</span><span style="color: green;">""</span>,<span style="color: green;">"formalSpell":</span><span style="color: green;">""</span>,<span style="color: green;">"formalWb":</span><span style="color: green;">""</span>,<span style="color: green;">"otherName":</span><span style="color: green;">""</span>,<span style="color: green;">"otherSpell":</span><span style="color: green;">""</span>,<span style="color: green;">"otherWb":</span><span style="color: green;">""</span>,<span style="color: green;">"regularCustom":</span><span style="color: green;">""</span>,<span style="color: green;">"regularName":</span><span style="color: green;">""</span>,<span style="color: green;">"regularSpell":</span><span style="color: green;">""</span>,<span style="color: green;">"regularWb":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"tradeName":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1110u3010rc1110s010u3011u67e5u8be2u75c5u5e8au4fe1u606fu63a5u53e30a3ca20id3d1110u3010rc1110s010u3011u67e5u8be2u75c5u5e8au4fe1u606fu63a5u53e33e203ca3e">1.1.10【RC1.1.10S010】查询病床信息接口
<a id=1.1.10【RC1.1.10S010】查询病床信息接口> </a></h2>
<p></p>
<h3 id="-31">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/ward/bed/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
病床信息查询接口</p>
<h3 id="-32">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>bedCode</td>
<td>否</td>
<td></td>
<td>病床编码</td>
</tr>
<tr>
<td>deptCode</td>
<td>否</td>
<td></td>
<td>病区编码</td>
</tr>
<tr>
<td>roomCode</td>
<td>否</td>
<td></td>
<td>病室编码</td>
</tr>
<tr>
<td>roomUbedSum</td>
<td>否</td>
<td></td>
<td>空床数量</td>
</tr>
<tr>
<td>bedState</td>
<td>否</td>
<td></td>
<td>床位状态</td>
</tr>
<tr>
<td>bedPrice</td>
<td>否</td>
<td></td>
<td>床位价格</td>
</tr>
</tbody>
</table>
<h3 id="-33">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病床编码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedPrice</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床位价格</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bedState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">床位状态</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病区编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> roomCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">病室编码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> roomUbedSum</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">空床数量</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-11">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"bedCode":</span><span style="color: green;">""</span>,<span style="color: green;">"bedPrice":</span><span style="color: green;">""</span>,<span style="color: green;">"bedState":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"roomCode":</span><span style="color: green;">""</span>,<span style="color: green;">"roomUbedSum":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1111u3010rc1111s011u3011u67e5u8be2u533bu9662u4fe1u606fu63a5u53e30a3ca20id3d1111u3010rc1111s011u3011u67e5u8be2u533bu9662u4fe1u606fu63a5u53e33e203ca3e">1.1.11【RC1.1.11S011】查询医院信息接口
<a id=1.1.11【RC1.1.11S011】查询医院信息接口> </a></h2>
<p></p>
<h3 id="-34">基本信息</h3>
<p><strong>Path：</strong> /api/v1/hospital/get</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询医院信息</p>
<h3 id="-35">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>路径参数</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>hosID</td>
<td></td>
<td>医院编码</td>
</tr>
</tbody>
</table>
<h3 id="-36">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据库类型</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hosEnglishName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医院名称（英文）</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hosId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医院ID</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> hosName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医院名称</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> info</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">中文简介</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> infoEnglish</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">英文简介</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> remark</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">备注</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> version</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">程序版本</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-12">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"dataType":</span><span style="color: green;">""</span>,<span style="color: green;">"hosEnglishName":</span><span style="color: green;">""</span>,<span style="color: green;">"hosId":</span><span style="color: green;">""</span>,<span style="color: green;">"hosName":</span><span style="color: green;">""</span>,<span style="color: green;">"info":</span><span style="color: green;">""</span>,<span style="color: green;">"infoEnglish":</span><span style="color: green;">""</span>,<span style="color: green;">"remark":</span><span style="color: green;">""</span>,<span style="color: green;">"version":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1112u3010rc1112s012u3011u67e5u8be2u836fu54c1u7269u4ef7u9879u76eeu5217u88680a3ca20id3d1112u3010rc1112s012u3011u67e5u8be2u836fu54c1u7269u4ef7u9879u76eeu5217u88683e203ca3e">1.1.12【RC1.1.12S012】查询药品物价项目列表
<a id=1.1.12【RC1.1.12S012】查询药品物价项目列表> </a></h2>
<p></p>
<h3 id="-37">基本信息</h3>
<p><strong>Path：</strong> /api/v1/curr/pharmaceutical/drug/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询药品物价项目列表</p>
<h3 id="-38">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>sysClassList</td>
<td>否</td>
<td></td>
<td>系统类别</td>
</tr>
<tr>
<td>vagueName</td>
<td>否</td>
<td></td>
<td>项目名称</td>
</tr>
<tr>
<td>simpleSpell</td>
<td>否</td>
<td></td>
<td>拼音码</td>
</tr>
<tr>
<td>identifyKey</td>
<td>否</td>
<td></td>
<td>项目编码</td>
</tr>
</tbody>
</table>
<h3 id="-39">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doseModelCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">剂型编码</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> doseModelName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">剂型名称</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugQuality</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品性质 M麻醉 J1精神一 J2精神二 D毒 F放射 YZD易制毒 N非特殊管理</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品类别</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> drugTypeName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">药品类型名称</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室名称</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> identifyKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标识主码</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> packQty</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">包装数</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> price</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> producerCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生产厂家编码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> producerName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生产厂家名称</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regularName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">通用名</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specialFlag9</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">医保类别：0 未知，1甲类 2乙类 3丙类</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品码拼音码</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> validState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效性标识 1 在用 0停用 2 废弃</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-13">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"doseModelCode":</span><span style="color: green;">""</span>,<span style="color: green;">"doseModelName":</span><span style="color: green;">""</span>,<span style="color: green;">"drugQuality":</span><span style="color: green;">""</span>,<span style="color: green;">"drugType":</span><span style="color: green;">""</span>,<span style="color: green;">"drugTypeName":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"identifyKey":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"packQty":</span><span style="color: green;">""</span>,<span style="color: green;">"price":</span><span style="color: green;">""</span>,<span style="color: green;">"producerCode":</span><span style="color: green;">""</span>,<span style="color: green;">"producerName":</span><span style="color: green;">""</span>,<span style="color: green;">"regularName":</span><span style="color: green;">""</span>,<span style="color: green;">"specialFlag9":</span><span style="color: green;">""</span>,<span style="color: green;">"specs":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>,<span style="color: green;">"validState":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1113u3010rc1113s013u3011u67e5u8be2u975eu836fu54c1u7269u4ef7u9879u76eeu5217u88680a3ca20id3d1113u3010rc1113s013u3011u67e5u8be2u975eu836fu54c1u7269u4ef7u9879u76eeu5217u88683e203ca3e">1.1.13【RC1.1.13S013】查询非药品物价项目列表
<a id=1.1.13【RC1.1.13S013】查询非药品物价项目列表> </a></h2>
<p></p>
<h3 id="-40">基本信息</h3>
<p><strong>Path：</strong> /api/v1/curr/unpharmaceutical/undrug/eisaiOrDiagnose/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询非药品物价项目列表</p>
<h3 id="-41">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>sysClassList</td>
<td>否</td>
<td></td>
<td>系统类别</td>
</tr>
<tr>
<td>vagueName</td>
<td>否</td>
<td></td>
<td>项目名称</td>
</tr>
<tr>
<td>simpleSpell</td>
<td>否</td>
<td></td>
<td>拼音码</td>
</tr>
<tr>
<td>identifyKey</td>
<td>否</td>
<td></td>
<td>项目编码</td>
</tr>
</tbody>
</table>
<h3 id="-42">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> defaultSample</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">检查部位或样本</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室编码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> execDeptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">执行科室名称</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> gbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">国家编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> identifyKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">标识主码</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> price</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单价</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> producerCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生产厂家编码</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> producerName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生产厂家名称</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> specs</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规格</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">商品码拼音码</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sysClass</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">非药品类型</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unit</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> unitflag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">单位标识(0,明细; 1,组套)</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> validState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">有效性标识 1 在用 0停用 2 废弃</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-14">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"defaultSample":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"execDeptName":</span><span style="color: green;">""</span>,<span style="color: green;">"gbCode":</span><span style="color: green;">""</span>,<span style="color: green;">"identifyKey":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"price":</span><span style="color: green;">""</span>,<span style="color: green;">"producerCode":</span><span style="color: green;">""</span>,<span style="color: green;">"producerName":</span><span style="color: green;">""</span>,<span style="color: green;">"specs":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"sysClass":</span><span style="color: green;">""</span>,<span style="color: green;">"unit":</span><span style="color: green;">""</span>,<span style="color: green;">"unitflag":</span><span style="color: green;">""</span>,<span style="color: green;">"validState":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1114u3010rc1114s014u3011u67e5u8be2u533bu751fu6709u6743u9650u7684u79d1u5ba40a3ca20id3d1114u3010rc1114s014u3011u67e5u8be2u533bu751fu6709u6743u9650u7684u79d1u5ba43e203ca3e">1.1.14【RC1.1.14S014】查询医生有权限的科室
<a id=1.1.14【RC1.1.14S014】查询医生有权限的科室> </a></h2>
<p></p>
<h3 id="-43">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/dept/doctdept/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询医生有权限的科室</p>
<h3 id="-44">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>docCode</td>
<td>是</td>
<td></td>
<td>医生工号</td>
</tr>
<tr>
<td>deptType</td>
<td>否</td>
<td></td>
<td>科室类型</td>
</tr>
</tbody>
</table>
<h3 id="-45">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> alterMoney</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">警戒线</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleBegin</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期开始</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cycleEnd</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">周期结束</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室编码</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptEname</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室英文</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptPro</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">特殊科室类型</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室类型</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> ext1Flag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志1</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> extFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">扩展标志 －是否已经集中发送 0 未,1 已</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> mediTime</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发药时间</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员编码</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作时间</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> regdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否挂号科室</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> simpleName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室简称</span></td><td key=5></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> spellCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室拼音码</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> tatdeptFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否核算科室</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> wbCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室五笔码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-15">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"alterMoney":</span><span style="color: green;">0</span>,<span style="color: green;">"cycleBegin":</span><span style="color: green;">0</span>,<span style="color: green;">"cycleEnd":</span><span style="color: green;">0</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptEname":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"deptPro":</span><span style="color: green;">""</span>,<span style="color: green;">"deptType":</span><span style="color: green;">""</span>,<span style="color: green;">"ext1Flag":</span><span style="color: green;">""</span>,<span style="color: green;">"extFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"mediTime":</span><span style="color: green;">0</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"regdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"simpleName":</span><span style="color: green;">""</span>,<span style="color: green;">"spellCode":</span><span style="color: green;">""</span>,<span style="color: green;">"tatdeptFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"wbCode":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1115u3010rc1115s203u3011u67e5u8be2u75c5u533au5185u6240u6709u5a74u513fu5217u88680a3ca20id3d1115u3010rc1115s203u3011u67e5u8be2u75c5u533au5185u6240u6709u5a74u513fu5217u88683e203ca3e">1.1.15【RC1.1.15S203】查询病区内所有婴儿列表
<a id=1.1.15【RC1.1.15S203】查询病区内所有婴儿列表> </a></h2>
<p></p>
<h3 id="-46">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/baby/query</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询病区内所有婴儿列表</p>
<h3 id="-47">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>nurseCellCode</td>
<td>否</td>
<td></td>
<td>病区编码</td>
</tr>
<tr>
<td>deptCode</td>
<td>否</td>
<td></td>
<td>科室编码</td>
</tr>
<tr>
<td>inpatientNo</td>
<td>否</td>
<td></td>
<td>住院流水号</td>
</tr>
</tbody>
</table>
<h3 id="-48">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> babyInpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">婴儿住院流水号</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birtPlace</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地点</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthAddress</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthCertificateNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生证编号</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> birthday</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">生日</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> bloodCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">血型编码</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> cancelFlag</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">取消标志</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> facility</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">接生机构名称</span></td><td key=5></td></tr><tr key=0-1-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fatherAge</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">父亲年龄</span></td><td key=5></td></tr><tr key=0-1-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fatherCardnNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">父亲身份证号</span></td><td key=5></td></tr><tr key=0-1-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fatherName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">父亲姓名</span></td><td key=5></td></tr><tr key=0-1-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fatherNation</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">父亲民族</span></td><td key=5></td></tr><tr key=0-1-12><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fatherNationality</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">父亲国籍</span></td><td key=5></td></tr><tr key=0-1-13><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> gestation</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生孕周</span></td><td key=5></td></tr><tr key=0-1-14><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> happenNo</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">发生序号</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1-15><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> healthStatus</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">健康状况 0--良好 1--一般 2--差</span></td><td key=5></td></tr><tr key=0-1-16><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> height</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">身高</span></td><td key=5></td></tr><tr key=0-1-17><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> home</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">家庭住址</span></td><td key=5></td></tr><tr key=0-1-18><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">入院日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-19><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">住院流水号</span></td><td key=5></td></tr><tr key=0-1-20><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> issueDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">签发日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-21><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherAge</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲年龄</span></td><td key=5></td></tr><tr key=0-1-22><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherCardNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲身份证号</span></td><td key=5></td></tr><tr key=0-1-23><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherInpatientNo</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲住院流水号</span></td><td key=5></td></tr><tr key=0-1-24><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲姓名</span></td><td key=5></td></tr><tr key=0-1-25><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherNation</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲民族</span></td><td key=5></td></tr><tr key=0-1-26><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> motherNationality</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">母亲国籍</span></td><td key=5></td></tr><tr key=0-1-27><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> name</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">姓名</span></td><td key=5></td></tr><tr key=0-1-28><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-1-29><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> operDate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作日期</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-30><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> placeType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生地点分类</span></td><td key=5></td></tr><tr key=0-1-31><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> prepayOutdate</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出院日期(预约)</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-1-32><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> receiver</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">接生人</span></td><td key=5></td></tr><tr key=0-1-33><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> sexCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-34><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> status</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">出生证状态 1有效 2 作废</span></td><td key=5></td></tr><tr key=0-1-35><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> weight</span></td><td key=1><span>number</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">体重</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-16">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"babyInpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"birtPlace":</span><span style="color: green;">""</span>,<span style="color: green;">"birthAddress":</span><span style="color: green;">""</span>,<span style="color: green;">"birthCertificateNo":</span><span style="color: green;">""</span>,<span style="color: green;">"birthday":</span><span style="color: green;">""</span>,<span style="color: green;">"bloodCode":</span><span style="color: green;">""</span>,<span style="color: green;">"cancelFlag":</span><span style="color: green;">""</span>,<span style="color: green;">"facility":</span><span style="color: green;">""</span>,<span style="color: green;">"fatherAge":</span><span style="color: green;">""</span>,<span style="color: green;">"fatherCardnNo":</span><span style="color: green;">""</span>,<span style="color: green;">"fatherName":</span><span style="color: green;">""</span>,<span style="color: green;">"fatherNation":</span><span style="color: green;">""</span>,<span style="color: green;">"fatherNationality":</span><span style="color: green;">""</span>,<span style="color: green;">"gestation":</span><span style="color: green;">""</span>,<span style="color: green;">"happenNo":</span><span style="color: green;">0</span>,<span style="color: green;">"healthStatus":</span><span style="color: green;">""</span>,<span style="color: green;">"height":</span><span style="color: green;">0</span>,<span style="color: green;">"home":</span><span style="color: green;">""</span>,<span style="color: green;">"inDate":</span><span style="color: green;">""</span>,<span style="color: green;">"inpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"issueDate":</span><span style="color: green;">""</span>,<span style="color: green;">"motherAge":</span><span style="color: green;">""</span>,<span style="color: green;">"motherCardNo":</span><span style="color: green;">""</span>,<span style="color: green;">"motherInpatientNo":</span><span style="color: green;">""</span>,<span style="color: green;">"motherName":</span><span style="color: green;">""</span>,<span style="color: green;">"motherNation":</span><span style="color: green;">""</span>,<span style="color: green;">"motherNationality":</span><span style="color: green;">""</span>,<span style="color: green;">"name":</span><span style="color: green;">""</span>,<span style="color: green;">"operCode":</span><span style="color: green;">""</span>,<span style="color: green;">"operDate":</span><span style="color: green;">""</span>,<span style="color: green;">"placeType":</span><span style="color: green;">""</span>,<span style="color: green;">"prepayOutdate":</span><span style="color: green;">""</span>,<span style="color: green;">"receiver":</span><span style="color: green;">""</span>,<span style="color: green;">"sexCode":</span><span style="color: green;">""</span>,<span style="color: green;">"status":</span><span style="color: green;">""</span>,<span style="color: green;">"weight":</span><span style="color: green;">0</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="1116u3010rc1116s231u3011u67e5u8be2u79d1u5ba4u5728u9662u4ebau65700a3ca20id3d1116u3010rc1116s231u3011u67e5u8be2u79d1u5ba4u5728u9662u4ebau65703e203ca3e">1.1.16【RC1.1.16S231】查询科室在院人数
<a id=1.1.16【RC1.1.16S231】查询科室在院人数> </a></h2>
<p></p>
<h3 id="-49">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/in-patient/inState/count</p>
<p><strong>Method：</strong> GET</p>
<p><strong>接口描述：</strong><br>
查询科室在院人数</p>
<h3 id="-50">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>inState</td>
<td>否</td>
<td></td>
<td>inState</td>
</tr>
</tbody>
</table>
<h3 id="-51">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> count</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院状态对应的人数</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室代码</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> inState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">在院状态</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-17">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>[{<span style="color: green;">"count":</span><span style="color: green;">""</span>,<span style="color: green;">"deptCode":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"inState":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h1 id="12u63a5u53e3u6570u636eu8131u654f">1.2接口数据脱敏</h1>
<p></p>
<h2 id="local-111u3010rp111u3011u4fddu5b58u8131u654fu89c4u52190a3ca20id3dlocal-111u3010rp111u3011u4fddu5b58u8131u654fu89c4u52193e203ca3e">local-1.1.1【RP1.1.1】保存脱敏规则
<a id=local-1.1.1【RP1.1.1】保存脱敏规则> </a></h2>
<p></p>
<h3 id="-52">基本信息</h3>
<p><strong>Path：</strong> /api/v1/sensitive/rule/save</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
保存脱敏规则</p>
<h3 id="-53">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"undefined":""}</pre>
<h3 id="-54">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> success</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-18">出参JSON</h3>
<p>false</p>
<h2 id="local-112u3010rp112u3011u67e5u8be2u8131u654fu89c4u5219u4fe1u606fu5217u88680a3ca20id3dlocal-112u3010rp112u3011u67e5u8be2u8131u654fu89c4u5219u4fe1u606fu5217u88683e203ca3e">local-1.1.2【RP1.1.2】查询脱敏规则信息列表
<a id=local-1.1.2【RP1.1.2】查询脱敏规则信息列表> </a></h2>
<p></p>
<h3 id="-55">基本信息</h3>
<p><strong>Path：</strong> /api/v1/sensitive/rule/query</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
查询脱敏规则信息列表</p>
<h3 id="-56">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"undefined":""}</pre>
<h3 id="-57">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object []</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=3><span style="font-weight: '700'">item 类型: </span><span>object</span></p></td></tr><tr key=0-0-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> clazzDescribe</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性所属实体类名描述对应taskCnName</span></td><td key=5></td></tr><tr key=0-0-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> clazzName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性所属实体类名对应taskName</span></td><td key=5></td></tr><tr key=0-0-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> clazzNameCh</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性所属实体类名描述对应taskCnName</span></td><td key=5></td></tr><tr key=0-0-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> configuration</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">SensitiveRuleInfoTo</span></td><td key=5></td></tr><tr key=0-0-3-0><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> algorithmCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">脱敏方法编码</span></td><td key=5></td></tr><tr key=0-0-3-1><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> algorithmName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">脱敏方法名称</span></td><td key=5></td></tr><tr key=0-0-3-2><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> algorithmParam</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">具体脱敏方法参数</span></td><td key=5></td></tr><tr key=0-0-3-3><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> columnCnName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性编码</span></td><td key=5></td></tr><tr key=0-0-3-4><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> columnName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性名称</span></td><td key=5></td></tr><tr key=0-0-3-5><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> createTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">创建时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-0-3-6><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> dataType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性类型</span></td><td key=5></td></tr><tr key=0-0-3-7><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> effectiveFlag</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否有效 1-有效 0-无效</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-3-8><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> fieldDescribe</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性描述</span></td><td key=5></td></tr><tr key=0-0-3-9><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规则主键</span></td><td key=5></td></tr><tr key=0-0-3-10><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> operator</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">操作员</span></td><td key=5></td></tr><tr key=0-0-3-11><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> ruleConfigurationSq</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规则配置ID-前端生成得</span></td><td key=5></td></tr><tr key=0-0-3-12><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> secretKey</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">密钥</span></td><td key=5></td></tr><tr key=0-0-3-13><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> sensitiveFlag</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否脱敏 0-不脱敏 1-脱敏</span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-0-3-14><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> serviceId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">接口ID</span></td><td key=5></td></tr><tr key=0-0-3-15><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> serviceName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">接口服务名称</span></td><td key=5></td></tr><tr key=0-0-3-16><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> taskCnName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">实体名称</span></td><td key=5></td></tr><tr key=0-0-3-17><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> taskName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">实体名描述</span></td><td key=5></td></tr><tr key=0-0-3-18><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> updateTime</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">更新时间</span></td><td key=5><p key=4><span style="font-weight: '700'">format: </span><span>date-time</span></p></td></tr><tr key=0-0-3-19><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> wayCode</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">脱敏规则类型</span></td><td key=5></td></tr><tr key=0-0-3-20><td key=0><span style="padding-left: 40px"><span style="color: #8c8a8a">├─</span> wayName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">脱敏规则方法</span></td><td key=5></td></tr><tr key=0-0-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> dsState</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">是否脱敏</span></td><td key=5></td></tr><tr key=0-0-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fieldDescribe</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性描述对应columnDescription</span></td><td key=5></td></tr><tr key=0-0-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fieldName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性编码对应columnName</span></td><td key=5></td></tr><tr key=0-0-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fieldNameCh</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性名称对应columnCnName</span></td><td key=5></td></tr><tr key=0-0-8><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> fieldType</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据类型对应dataType</span></td><td key=5></td></tr><tr key=0-0-9><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> id</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">规则主键</span></td><td key=5></td></tr><tr key=0-0-10><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> packaddr</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">属性所属实体包路径</span></td><td key=5></td></tr><tr key=0-0-11><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> serviceId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">服务名ID</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> success</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-19">出参JSON</h3>
<pre>{<span style="color: green;">"data":</span>[{<span style="color: green;">"clazzDescribe":</span><span style="color: green;">""</span>,<span style="color: green;">"clazzName":</span><span style="color: green;">""</span>,<span style="color: green;">"clazzNameCh":</span><span style="color: green;">""</span>,<span style="color: green;">"configuration":</span><span style="color: green;">""</span>,<span style="color: green;">"dsState":</span><span style="color: green;">""</span>,<span style="color: green;">"fieldDescribe":</span><span style="color: green;">""</span>,<span style="color: green;">"fieldName":</span><span style="color: green;">""</span>,<span style="color: green;">"fieldNameCh":</span><span style="color: green;">""</span>,<span style="color: green;">"fieldType":</span><span style="color: green;">""</span>,<span style="color: green;">"id":</span><span style="color: green;">""</span>,<span style="color: green;">"packaddr":</span><span style="color: green;">""</span>,<span style="color: green;">"serviceId":</span><span style="color: green;">""</span>}],<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h2 id="local-113u3010rp113u3011u6e05u7a7au8131u654fu89c4u5219u4fe1u606f0a3ca20id3dlocal-113u3010rp113u3011u6e05u7a7au8131u654fu89c4u5219u4fe1u606f3e203ca3e">local-1.1.3【RP1.1.3】清空脱敏规则信息
<a id=local-1.1.3【RP1.1.3】清空脱敏规则信息> </a></h2>
<p></p>
<h3 id="-58">基本信息</h3>
<p><strong>Path：</strong> /api/v1/sensitive/rule/clear</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
清空脱敏规则信息</p>
<h3 id="-59">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"undefined":""}</pre>
<h3 id="-60">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> success</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-20">出参JSON</h3>
<p>false</p>
<h2 id="local-114u3010rp114u3011u65b0u589eu8131u654fu57fau7840u6570u636e0a3ca20id3dlocal-114u3010rp114u3011u65b0u589eu8131u654fu57fau7840u6570u636e3e203ca3e">local-1.1.4【RP1.1.4】新增脱敏基础数据
<a id=local-1.1.4【RP1.1.4】新增脱敏基础数据> </a></h2>
<p></p>
<h3 id="-61">基本信息</h3>
<p><strong>Path：</strong> /api/v1/sensitive/rule/add</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
新增脱敏基础数据</p>
<h3 id="-62">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> </span></td><td key=1><span></span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"undefined":""}</pre>
<h3 id="-63">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> success</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-21">出参JSON</h3>
<p>false</p>
<h1 id="13u767bu5f55u670du52a1">1.3登录服务</h1>
<p></p>
<h2 id="131u3010rc131s189u3011u533bu751fu767bu5f550a3ca20id3d131u3010rc131s189u3011u533bu751fu767bu5f553e203ca3e">1.3.1【RC1.3.1S189】医生登录
<a id=1.3.1【RC1.3.1S189】医生登录> </a></h2>
<p></p>
<h3 id="-64">基本信息</h3>
<p><strong>Path：</strong> /api/v1/common/login</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
登录(大象)</p>
<h3 id="-65">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Body</strong></p>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> loginId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">院内HIS系统账号</span></td><td key=5></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> password</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">密码</span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> userName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">账号名称</span></td><td key=5></td></tr>
               </tbody>
              </table>
            **入参样例**
<pre>{"loginId":"","password":"","userName":""}</pre>
<h3 id="-66">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> code</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>object</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">【登录信息to】</span></td><td key=5></td></tr><tr key=0-1-0><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室ID</span></td><td key=5></td></tr><tr key=0-1-1><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> deptName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">科室名称</span></td><td key=5></td></tr><tr key=0-1-2><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplId</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工ID</span></td><td key=5></td></tr><tr key=0-1-3><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> emplName</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">员工姓名</span></td><td key=5></td></tr><tr key=0-1-4><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> enable</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">数据有效状态</span></td><td key=5></td></tr><tr key=0-1-5><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> gender</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">性别</span></td><td key=5></td></tr><tr key=0-1-6><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> level</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap">职级名称</span></td><td key=5></td></tr><tr key=0-1-7><td key=0><span style="padding-left: 20px"><span style="color: #8c8a8a">├─</span> reg</span></td><td key=1><span>boolean</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-22">出参JSON</h3>
<pre>{<span style="color: green;">"code":</span><span style="color: green;">""</span>,<span style="color: green;">"data":</span>{<span style="color: green;">"deptId":</span><span style="color: green;">""</span>,<span style="color: green;">"deptName":</span><span style="color: green;">""</span>,<span style="color: green;">"emplId":</span><span style="color: green;">""</span>,<span style="color: green;">"emplName":</span><span style="color: green;">""</span>,<span style="color: green;">"enable":</span><span style="color: green;">false</span>,<span style="color: green;">"gender":</span><span style="color: green;">""</span>,<span style="color: green;">"level":</span><span style="color: green;">""</span>,<span style="color: green;">"reg":</span><span style="color: green;">false</span>},<span style="color: green;">"msg":</span><span style="color: green;">""</span>}</pre>
<h1 id="u63a5u53e3u6570u636eu8131u654f">接口数据脱敏</h1>
<p></p>
<h2 id="local-115u3010rp115u3011u6d4bu8bd5u8131u654fu51fdu65700a3ca20id3dlocal-115u3010rp115u3011u6d4bu8bd5u8131u654fu51fdu65703e203ca3e">local-1.1.5【RP1.1.5】测试脱敏函数
<a id=local-1.1.5【RP1.1.5】测试脱敏函数> </a></h2>
<p></p>
<h3 id="-67">基本信息</h3>
<p><strong>Path：</strong> /api/v1/sensitive/rule/test</p>
<p><strong>Method：</strong> POST</p>
<p><strong>接口描述：</strong><br>
测试脱敏函数</p>
<h3 id="-68">请求参数</h3>
<p><strong>Headers</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>参数值</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>Content-Type</td>
<td>application/json</td>
<td>是</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p><strong>Query</strong></p>
<table>
<thead>
<tr>
<th>参数名称</th>
<th>是否必须</th>
<th>示例</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>fieldCode</td>
<td>否</td>
<td></td>
<td>fieldCode</td>
</tr>
<tr>
<td>fieldValue</td>
<td>否</td>
<td></td>
<td>fieldValue</td>
</tr>
</tbody>
</table>
<h3 id="-69">返回数据</h3>
<table>
  <thead class="ant-table-thead">
    <tr>
      <th key=name>名称</th><th key=type>类型</th><th key=required>是否必须</th><th key=default>默认值</th><th key=desc>备注</th><th key=sub>其他信息</th>
    </tr>
  </thead><tbody className="ant-table-tbody"><tr key=0-0><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> data</span></td><td key=1><span>integer</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5><p key=2><span style="font-weight: '700'">format: </span><span>int32</span></p></td></tr><tr key=0-1><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> msg</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr><tr key=0-2><td key=0><span style="padding-left: 0px"><span style="color: #8c8a8a"></span> success</span></td><td key=1><span>string</span></td><td key=2>非必须</td><td key=3></td><td key=4><span style="white-space: pre-wrap"></span></td><td key=5></td></tr>
               </tbody>
              </table>
<h3 id="json-23">出参JSON</h3>
<p>false</p>

            <footer class="m-footer">
              <p>Build by <a href="https://ymfe.org/">YMFE</a>.</p>
            </footer>
          </div>
        </div>
      </body>
      </html>
      